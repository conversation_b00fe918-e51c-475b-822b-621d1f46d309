<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.5.1" type="incidents">

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/fk/creeperbox/MainActivity.java"
            line="34"
            column="20"
            startOffset="945"
            endLine="34"
            endColumn="40"
            endOffset="965"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/fk/creeperbox/MainActivity.java"
            line="38"
            column="31"
            startOffset="1079"
            endLine="38"
            endColumn="52"
            endOffset="1100"/>
    </incident>

    <incident
        id="SetWorldReadable"
        severity="warning"
        message="Setting file permissions to world-readable can be risky, review carefully">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/fk/creeperbox/MainActivity.java"
            line="108"
            column="25"
            startOffset="4102"
            endLine="108"
            endColumn="60"
            endOffset="4137"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields; this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/fk/creeperbox/XposedMain.java"
            line="30"
            column="13"
            startOffset="956"
            endLine="30"
            endColumn="19"
            endOffset="962"/>
    </incident>

    <incident
        id="UnsafeDynamicallyLoadedCode"
        severity="warning"
        message="Dynamically loading code using `load` is risky, please use `loadLibrary` instead when possible">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/fk/creeperbox/XposedMain.java"
            line="135"
            column="21"
            startOffset="6702"
            endLine="135"
            endColumn="50"
            endOffset="6731"/>
    </incident>

    <incident
        id="SetWorldReadable"
        severity="warning"
        message="Setting file permissions to world-readable can be risky, review carefully">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/fk/creeperbox/XposedMain.java"
            line="201"
            column="21"
            startOffset="9587"
            endLine="201"
            endColumn="56"
            endOffset="9622"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="12"
            column="9"
            startOffset="208"
            endLine="12"
            endColumn="23"
            endOffset="222"/>
    </incident>

    <incident
        id="ChromeOsAbiSupport"
        severity="warning"
        message="Missing x86_64 ABI support for ChromeOS">
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="19"
            column="27"
            startOffset="403"
            endLine="19"
            endColumn="46"
            endOffset="422"/>
    </incident>

</incidents>
