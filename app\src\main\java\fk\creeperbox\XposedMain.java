package fk.creeperbox;

import android.app.Application;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import java.io.File;

import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

public class XposedMain implements IXposedHookLoadPackage {

    private static final String MODULE_PACKAGE_NAME = "fk.creeperbox";
    private static volatile boolean isInjectionTriggered = false;

    @Override
    public void handleLoadPackage(final XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {
        if (lpparam.isFirstApplication) {
            XposedBridge.log("MemPatcher: ✅ Targeting package: " + lpparam.packageName);

            try {
                XposedHelpers.findAndHookMethod(Application.class, "onCreate", new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        super.afterHookedMethod(param);
                        
                        if (!isInjectionTriggered) {
                            isInjectionTriggered = true;
                            Application app = (Application) param.thisObject;
                            Context targetContext = app.getApplicationContext();
                            
                            // 在后台线程加载SO
                            new Thread(() -> injectMemPatcher(targetContext)).start();
                        }
                    }
                });
            } catch (Throwable t) {
                XposedBridge.log("MemPatcher: ❌ Failed to hook Application.onCreate: " + t.getMessage());
            }
        }
    }

    private void injectMemPatcher(Context targetContext) {
        try {
            // 获取模块信息
            ApplicationInfo moduleAppInfo = targetContext.getPackageManager()
                .getApplicationInfo(MODULE_PACKAGE_NAME, 0);
            
            // 构建SO文件路径
            String memPatcherSoPath = moduleAppInfo.nativeLibraryDir + File.separator + "libmempatcher.so";
            
            // 验证文件存在
            File memPatcherSoFile = new File(memPatcherSoPath);
            if (!memPatcherSoFile.exists()) {
                XposedBridge.log("MemPatcher: ❌ SO file not found: " + memPatcherSoPath);
                return;
            }
            
            // 加载SO文件
            System.load(memPatcherSoPath);
            
            XposedBridge.log("MemPatcher: ✅ Injection successful!");
            
        } catch (PackageManager.NameNotFoundException e) {
            XposedBridge.log("MemPatcher: ❌ Module package not found");
        } catch (Throwable t) {
            XposedBridge.log("MemPatcher: ❌ Injection failed: " + t.getMessage());
        }
    }
}
