C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:6: Warning: WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the MediaStore.createWriteRequest intent. [ScopedStorage]
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:7: Warning: READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: READ_MEDIA_IMAGES, READ_MEDIA_VIDEO or READ_MEDIA_AUDIO. [ScopedStorage]
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScopedStorage":
   Scoped storage is enforced on Android 10+ (or Android 11+ if using
   requestLegacyExternalStorage). In particular, WRITE_EXTERNAL_STORAGE will
   no longer provide write access to all files; it will provide the equivalent
   of READ_EXTERNAL_STORAGE instead.

   As of Android 13, if you need to query or interact with MediaStore or media
   files on the shared storage, you should be using instead one or more new
   storage permissions:
   * android.permission.READ_MEDIA_IMAGES
   * android.permission.READ_MEDIA_VIDEO
   * android.permission.READ_MEDIA_AUDIO

   and then add maxSdkVersion="33" to the older permission. See the developer
   guide for how to do this:
   https://developer.android.com/about/versions/13/behavior-changes-13#granula
   r-media-permissions

   The MANAGE_EXTERNAL_STORAGE permission can be used to manage all files, but
   it is rarely necessary and most apps on Google Play are not allowed to use
   it. Most apps should instead migrate to use scoped storage. To modify or
   delete files, apps should request write access from the user as described
   at https://goo.gle/android-mediastore-createwriterequest.

   To learn more, read these resources: Play policy:
   https://goo.gle/policy-storage-help Allowable use cases:
   https://goo.gle/policy-storage-usecases

   https://goo.gle/android-storage-usecases

C:\Users\<USER>\Desktop\zero\app\build.gradle.kts:12: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk = 34
        ~~~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application runs on a version of Android that is more recent than
   your targetSdkVersion specifies that it has been tested with, various
   compatibility modes kick in. This ensures that your application continues
   to work, but it may look out of place. For example, if the targetSdkVersion
   is less than 14, your app may get an option button in the UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

C:\Users\<USER>\Desktop\zero\app\build.gradle.kts:19: Warning: Missing x86_64 ABI support for ChromeOS [ChromeOsAbiSupport]
            abiFilters += listOf("arm64-v8a")
                          ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ChromeOsAbiSupport":
   To properly support ChromeOS, your Android application should have an x86
   and/or x86_64 binary as part of the build configuration. To fix the issue,
   ensure your files are properly optimized for ARM; the binary translator
   will then ensure compatibility with x86. Alternatively, add an abiSplit for
   x86 within your build.gradle file and create the required x86
   dependencies.

   https://developer.android.com/ndk/guides/abis

C:\Users\<USER>\Desktop\zero\app\src\main\java\fk\creeperbox\MainActivity.java:108: Warning: Setting file permissions to world-readable can be risky, review carefully [SetWorldReadable]
                        targetFile.setReadable(true, false);
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\zero\app\src\main\java\fk\creeperbox\XposedMain.java:201: Warning: Setting file permissions to world-readable can be risky, review carefully [SetWorldReadable]
                    targetFile.setReadable(true, false);
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetWorldReadable":
   Setting files world-readable is very dangerous, and likely to cause
   security holes in applications. It is strongly discouraged; instead,
   applications should use more formal mechanisms for interactions such as
   ContentProvider, BroadcastReceiver, and Service.

   https://goo.gle/SetWorldReadable

C:\Users\<USER>\Desktop\zero\app\src\main\java\fk\creeperbox\XposedMain.java:135: Warning: Dynamically loading code using load is risky, please use loadLibrary instead when possible [UnsafeDynamicallyLoadedCode]
                    System.load(memPatcherSoPath);
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnsafeDynamicallyLoadedCode":
   Dynamically loading code from locations other than the application's
   library directory or the Android platform's built-in library directories is
   dangerous, as there is an increased risk that the code could have been
   tampered with. Applications should use loadLibrary when possible, which
   provides increased assurance that libraries are loaded from one of these
   safer locations. Application developers should use the features of their
   development environment to place application native libraries into the lib
   directory of their compiled APKs.

C:\Users\<USER>\Desktop\zero\app\src\main\java\fk\creeperbox\XposedMain.java:30: Warning: Do not place Android context classes in static fields; this is a memory leak [StaticFieldLeak]
    private static Context targetContext = null;
            ~~~~~~

   Explanation for issues of type "StaticFieldLeak":
   A static field will leak contexts.

   Non-static inner classes have an implicit reference to their outer class.
   If that outer class is for example a Fragment or Activity, then this
   reference means that the long-running handler/loader/task will hold a
   reference to the activity which prevents it from getting garbage
   collected.

   Similarly, direct field references to activities and fragments from these
   longer running instances can cause leaks.

   ViewModel classes should never point to Views or non-application Contexts.

C:\Users\<USER>\Desktop\zero\app\src\main\java\fk\creeperbox\MainActivity.java:34: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        tv.setText("适用于1.0.9ver1 外传司马马");
                   ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\zero\app\src\main\java\fk\creeperbox\MainActivity.java:38: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        extractButton.setText("提取 libmempatcher.so");
                              ~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

0 errors, 10 warnings
