package fk.creeperbox;

import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import fk.creeperbox.databinding.ActivityMainBinding;

public class MainActivity extends AppCompatActivity {

    private ActivityMainBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        
        // 显示简单信息
        binding.sampleText.setText("MemPatcher Xposed Module\n模块已安装成功\n\n请在Xposed框架中激活此模块");
    }
}