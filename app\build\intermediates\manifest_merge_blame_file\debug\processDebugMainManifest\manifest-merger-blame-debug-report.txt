1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="fk.creeperbox"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions for file extraction -->
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:6:5-81
12-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:7:5-80
13-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:7:22-77
14
15    <permission
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\02ad785632a850942a66559c2c31b62e\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
16        android:name="fk.creeperbox.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\02ad785632a850942a66559c2c31b62e\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\02ad785632a850942a66559c2c31b62e\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="fk.creeperbox.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\02ad785632a850942a66559c2c31b62e\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\02ad785632a850942a66559c2c31b62e\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:7:84-38:19
22        android:allowBackup="true"
22-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:8:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\02ad785632a850942a66559c2c31b62e\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:9:9-65
25        android:debuggable="true"
26        android:extractNativeLibs="true"
27        android:fullBackupContent="@xml/backup_rules"
27-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:10:9-54
28        android:icon="@mipmap/ic_launcher"
28-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:11:9-43
29        android:label="@string/app_name"
29-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:12:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:13:9-54
31        android:supportsRtl="true"
31-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:14:9-35
32        android:testOnly="true"
33        android:theme="@style/Theme.苦力怕破解器" >
33-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:15:9-44
34
35        <!-- Xposed module metadata -->
36        <meta-data
36-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:19:9-21:36
37            android:name="xposedmodule"
37-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:20:13-40
38            android:value="true" />
38-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:21:13-33
39        <meta-data
39-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:22:9-24:75
40            android:name="xposeddescription"
40-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:23:13-45
41            android:value="Memory Patcher - Native SO injection module" />
41-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:24:13-72
42        <meta-data
42-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:25:9-27:34
43            android:name="xposedminversion"
43-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:26:13-44
44            android:value="82" />
44-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:27:13-31
45
46        <activity
46-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:29:9-37:20
47            android:name="fk.creeperbox.MainActivity"
47-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:30:13-41
48            android:exported="true" >
48-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:31:13-36
49            <intent-filter>
49-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:32:13-36:29
50                <action android:name="android.intent.action.MAIN" />
50-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:33:17-69
50-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:33:25-66
51
52                <category android:name="android.intent.category.LAUNCHER" />
52-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:35:17-77
52-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:35:27-74
53            </intent-filter>
54        </activity>
55
56        <provider
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
57            android:name="androidx.startup.InitializationProvider"
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
58            android:authorities="fk.creeperbox.androidx-startup"
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
59            android:exported="false" >
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
60            <meta-data
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.emoji2.text.EmojiCompatInitializer"
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
62                android:value="androidx.startup" />
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cddf3204b8317652a6d39a1031952bc8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
64-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cddf3204b8317652a6d39a1031952bc8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
65                android:value="androidx.startup" />
65-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cddf3204b8317652a6d39a1031952bc8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
66            <meta-data
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
68                android:value="androidx.startup" />
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
69        </provider>
70
71        <receiver
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
72            android:name="androidx.profileinstaller.ProfileInstallReceiver"
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
73            android:directBootAware="false"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
74            android:enabled="true"
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
75            android:exported="true"
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
76            android:permission="android.permission.DUMP" >
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
78                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
81                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
84                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
87                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
88            </intent-filter>
89        </receiver>
90    </application>
91
92</manifest>
