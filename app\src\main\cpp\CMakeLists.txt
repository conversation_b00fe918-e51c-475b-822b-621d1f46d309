cmake_minimum_required(VERSION 3.18.1)

# Set C standard
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Android specific settings
if(ANDROID)
    set(CMAKE_ANDROID_STL_TYPE c++_static)
endif()

# Project configuration
project(mempatcher C)

# Build configuration
set(CMAKE_BUILD_TYPE Release)

# Compiler flags for optimization and Android compatibility
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -ffunction-sections -fdata-sections -fPIC")
set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -O2 -DNDEBUG")

# Linker flags
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,--gc-sections -Wl,--export-dynamic")

# Set Frida installation path - REQUIRED
set(FRIDA_INSTALL_DIR "${CMAKE_CURRENT_SOURCE_DIR}/frida" CACHE PATH "Path to Frida installation")

# Include directories
include_directories(
    ${FRIDA_INSTALL_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Set Frida library path directly
set(FRIDA_CORE_LIB "${FRIDA_INSTALL_DIR}/libfrida-core.a")

# Verify Frida library exists
if(NOT EXISTS ${FRIDA_CORE_LIB})
    message(FATAL_ERROR "Frida core library not found at: ${FRIDA_CORE_LIB}")
endif()

message(STATUS "Using Frida library: ${FRIDA_CORE_LIB}")

# Create the shared library
add_library(mempatcher SHARED
    custom_gadget.c
)

# Link libraries - Frida is REQUIRED
target_link_libraries(mempatcher
    ${FRIDA_CORE_LIB}
    log          # Android logging
    dl           # Dynamic linking
    m            # Math library
    z            # Zlib compression
    
    # Additional libraries that Frida may need
    atomic       # Atomic operations
)

# Set target properties
set_target_properties(mempatcher PROPERTIES
    VERSION 1.0
    SOVERSION 1
    OUTPUT_NAME "mempatcher"
)

# Android specific linking
if(ANDROID)
    target_link_libraries(mempatcher
        android      # Android native API
    )
endif()

# Installation rules
install(TARGETS mempatcher
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# Print configuration information
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Frida install dir: ${FRIDA_INSTALL_DIR}")
message(STATUS "Frida core library: ${FRIDA_CORE_LIB}")
message(STATUS "Android ABI: ${ANDROID_ABI}")
message(STATUS "Android NDK: ${ANDROID_NDK}")

# Additional build information
if(ANDROID)
    message(STATUS "Target Android API: ${ANDROID_PLATFORM}")
    message(STATUS "Android STL: ${CMAKE_ANDROID_STL_TYPE}")
endif()