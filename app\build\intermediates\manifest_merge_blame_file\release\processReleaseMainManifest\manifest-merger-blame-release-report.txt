1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="fk.creeperbox"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions for file extraction -->
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:6:5-81
12-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:7:5-80
13-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:7:22-77
14
15    <permission
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\02ad785632a850942a66559c2c31b62e\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
16        android:name="fk.creeperbox.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\02ad785632a850942a66559c2c31b62e\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\02ad785632a850942a66559c2c31b62e\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="fk.creeperbox.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\02ad785632a850942a66559c2c31b62e\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\02ad785632a850942a66559c2c31b62e\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:8:7-40:19
22        android:allowBackup="true"
22-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:9:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\02ad785632a850942a66559c2c31b62e\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:10:9-65
25        android:extractNativeLibs="true"
25-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:17:9-41
26        android:fullBackupContent="@xml/backup_rules"
26-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:13:9-41
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:14:9-54
30        android:supportsRtl="true"
30-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:15:9-35
31        android:theme="@style/Theme.苦力怕破解器" >
31-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:16:9-44
32
33        <!-- Xposed module metadata -->
34        <meta-data
34-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:21:9-23:36
35            android:name="xposedmodule"
35-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:22:13-40
36            android:value="true" />
36-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:23:13-33
37        <meta-data
37-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:24:9-26:75
38            android:name="xposeddescription"
38-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:25:13-45
39            android:value="Memory Patcher - Native SO injection module" />
39-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:26:13-72
40        <meta-data
40-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:27:9-29:34
41            android:name="xposedminversion"
41-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:28:13-44
42            android:value="82" />
42-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:29:13-31
43
44        <activity
44-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:31:9-39:20
45            android:name="fk.creeperbox.MainActivity"
45-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:32:13-41
46            android:exported="true" >
46-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:33:13-36
47            <intent-filter>
47-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:34:13-38:29
48                <action android:name="android.intent.action.MAIN" />
48-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:35:17-69
48-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:35:25-66
49
50                <category android:name="android.intent.category.LAUNCHER" />
50-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:37:17-77
50-->C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml:37:27-74
51            </intent-filter>
52        </activity>
53
54        <provider
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
55            android:name="androidx.startup.InitializationProvider"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
56            android:authorities="fk.creeperbox.androidx-startup"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
57            android:exported="false" >
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
58            <meta-data
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.emoji2.text.EmojiCompatInitializer"
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
60                android:value="androidx.startup" />
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f551997c6023d49fd9f79949d57d30e\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cddf3204b8317652a6d39a1031952bc8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
62-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cddf3204b8317652a6d39a1031952bc8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
63                android:value="androidx.startup" />
63-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cddf3204b8317652a6d39a1031952bc8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
66                android:value="androidx.startup" />
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
67        </provider>
68
69        <receiver
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
70            android:name="androidx.profileinstaller.ProfileInstallReceiver"
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
71            android:directBootAware="false"
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
72            android:enabled="true"
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
73            android:exported="true"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
74            android:permission="android.permission.DUMP" >
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
76                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
79                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
82                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
85                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\34cab8c59b08372902ac4f479116e1ca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
86            </intent-filter>
87        </receiver>
88    </application>
89
90</manifest>
