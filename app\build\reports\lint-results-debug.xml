<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.5.1">

    <issue
        id="ScopedStorage"
        severity="Warning"
        message="WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the `MediaStore.createWriteRequest` intent."
        category="Correctness"
        priority="8"
        summary="Affected by scoped storage"
        explanation="Scoped storage is enforced on Android 10+ (or Android 11+ if using `requestLegacyExternalStorage`). In particular, `WRITE_EXTERNAL_STORAGE` will no longer provide write access to all files; it will provide the equivalent of `READ_EXTERNAL_STORAGE` instead.&#xA;&#xA;As of Android 13, if you need to query or interact with MediaStore or media files on the shared storage, you should be using instead one or more new storage permissions:&#xA;* `android.permission.READ_MEDIA_IMAGES`&#xA;* `android.permission.READ_MEDIA_VIDEO`&#xA;* `android.permission.READ_MEDIA_AUDIO`&#xA;&#xA;and then add `maxSdkVersion=&quot;33&quot;` to the older permission. See the developer guide for how to do this: https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions&#xA;&#xA;The `MANAGE_EXTERNAL_STORAGE` permission can be used to manage all files, but it is rarely necessary and most apps on Google Play are not allowed to use it. Most apps should instead migrate to use scoped storage. To modify or delete files, apps should request write access from the user as described at https://goo.gle/android-mediastore-createwriterequest.&#xA;&#xA;To learn more, read these resources: Play policy: https://goo.gle/policy-storage-help Allowable use cases: https://goo.gle/policy-storage-usecases"
        url="https://goo.gle/android-storage-usecases"
        urls="https://goo.gle/android-storage-usecases"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.WRITE_EXTERNAL_STORAGE&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml"
            line="6"
            column="36"/>
    </issue>

    <issue
        id="ScopedStorage"
        severity="Warning"
        message="READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: `READ_MEDIA_IMAGES`, `READ_MEDIA_VIDEO` or `READ_MEDIA_AUDIO`."
        category="Correctness"
        priority="8"
        summary="Affected by scoped storage"
        explanation="Scoped storage is enforced on Android 10+ (or Android 11+ if using `requestLegacyExternalStorage`). In particular, `WRITE_EXTERNAL_STORAGE` will no longer provide write access to all files; it will provide the equivalent of `READ_EXTERNAL_STORAGE` instead.&#xA;&#xA;As of Android 13, if you need to query or interact with MediaStore or media files on the shared storage, you should be using instead one or more new storage permissions:&#xA;* `android.permission.READ_MEDIA_IMAGES`&#xA;* `android.permission.READ_MEDIA_VIDEO`&#xA;* `android.permission.READ_MEDIA_AUDIO`&#xA;&#xA;and then add `maxSdkVersion=&quot;33&quot;` to the older permission. See the developer guide for how to do this: https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions&#xA;&#xA;The `MANAGE_EXTERNAL_STORAGE` permission can be used to manage all files, but it is rarely necessary and most apps on Google Play are not allowed to use it. Most apps should instead migrate to use scoped storage. To modify or delete files, apps should request write access from the user as described at https://goo.gle/android-mediastore-createwriterequest.&#xA;&#xA;To learn more, read these resources: Play policy: https://goo.gle/policy-storage-help Allowable use cases: https://goo.gle/policy-storage-usecases"
        url="https://goo.gle/android-storage-usecases"
        urls="https://goo.gle/android-storage-usecases"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_EXTERNAL_STORAGE&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\zero\app\src\main\AndroidManifest.xml"
            line="7"
            column="36"/>
    </issue>

    <issue
        id="OldTargetApi"
        severity="Warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details."
        category="Correctness"
        priority="6"
        summary="Target SDK attribute is not targeting latest version"
        explanation="When your application runs on a version of Android that is more recent than your `targetSdkVersion` specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the `targetSdkVersion` is less than 14, your app may get an option button in the UI.&#xA;&#xA;To fix this issue, set the `targetSdkVersion` to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: https://developer.android.com/reference/android/os/Build.VERSION_CODES.html as well as follow this guide:&#xA;https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        urls="https://developer.android.com/distribute/best-practices/develop/target-sdk.html,https://developer.android.com/reference/android/os/Build.VERSION_CODES.html"
        errorLine1="        targetSdk = 34"
        errorLine2="        ~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\zero\app\build.gradle.kts"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="ChromeOsAbiSupport"
        severity="Warning"
        message="Missing x86_64 ABI support for ChromeOS"
        category="Correctness:Chrome OS"
        priority="4"
        summary="Missing ABI Support for ChromeOS"
        explanation="To properly support ChromeOS, your Android application should have an x86 and/or x86_64 binary as part of the build configuration. To fix the issue, ensure your files are properly optimized for ARM; the binary translator will then ensure compatibility with x86. Alternatively, add an `abiSplit` for x86 within your `build.gradle` file and create the required x86 dependencies."
        url="https://developer.android.com/ndk/guides/abis"
        urls="https://developer.android.com/ndk/guides/abis"
        errorLine1="            abiFilters += listOf(&quot;arm64-v8a&quot;)"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\zero\app\build.gradle.kts"
            line="19"
            column="27"/>
    </issue>

    <issue
        id="SetWorldReadable"
        severity="Warning"
        message="Setting file permissions to world-readable can be risky, review carefully"
        category="Security"
        priority="6"
        summary="`File.setReadable()` used to make file world-readable"
        explanation="Setting files world-readable is very dangerous, and likely to cause security holes in applications. It is strongly discouraged; instead, applications should use more formal mechanisms for interactions such as `ContentProvider`, `BroadcastReceiver`, and `Service`."
        url="https://goo.gle/SetWorldReadable"
        urls="https://goo.gle/SetWorldReadable"
        errorLine1="                        targetFile.setReadable(true, false);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\zero\app\src\main\java\fk\creeperbox\MainActivity.java"
            line="108"
            column="25"/>
    </issue>

    <issue
        id="SetWorldReadable"
        severity="Warning"
        message="Setting file permissions to world-readable can be risky, review carefully"
        category="Security"
        priority="6"
        summary="`File.setReadable()` used to make file world-readable"
        explanation="Setting files world-readable is very dangerous, and likely to cause security holes in applications. It is strongly discouraged; instead, applications should use more formal mechanisms for interactions such as `ContentProvider`, `BroadcastReceiver`, and `Service`."
        url="https://goo.gle/SetWorldReadable"
        urls="https://goo.gle/SetWorldReadable"
        errorLine1="                    targetFile.setReadable(true, false);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\zero\app\src\main\java\fk\creeperbox\XposedMain.java"
            line="201"
            column="21"/>
    </issue>

    <issue
        id="UnsafeDynamicallyLoadedCode"
        severity="Warning"
        message="Dynamically loading code using `load` is risky, please use `loadLibrary` instead when possible"
        category="Security"
        priority="4"
        summary="`load` used to dynamically load code"
        explanation="Dynamically loading code from locations other than the application&apos;s library directory or the Android platform&apos;s built-in library directories is dangerous, as there is an increased risk that the code could have been tampered with. Applications should use `loadLibrary` when possible, which provides increased assurance that libraries are loaded from one of these safer locations. Application developers should use the features of their development environment to place application native libraries into the lib directory of their compiled APKs."
        errorLine1="                    System.load(memPatcherSoPath);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\zero\app\src\main\java\fk\creeperbox\XposedMain.java"
            line="135"
            column="21"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        severity="Warning"
        message="Do not place Android context classes in static fields; this is a memory leak"
        category="Performance"
        priority="6"
        summary="Static Field Leaks"
        explanation="A static field will leak contexts.&#xA;&#xA;Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a `Fragment` or `Activity`, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.&#xA;&#xA;Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.&#xA;&#xA;ViewModel classes should never point to Views or non-application Contexts."
        errorLine1="    private static Context targetContext = null;"
        errorLine2="            ~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\zero\app\src\main\java\fk\creeperbox\XposedMain.java"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        tv.setText(&quot;适用于1.0.9ver1 外传司马马&quot;);"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\zero\app\src\main\java\fk\creeperbox\MainActivity.java"
            line="34"
            column="20"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        extractButton.setText(&quot;提取 libmempatcher.so&quot;);"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\zero\app\src\main\java\fk\creeperbox\MainActivity.java"
            line="38"
            column="31"/>
    </issue>

</issues>
