package crk.creeperbox;

import android.app.Application;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;

import java.io.File;

import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

public class FridaGadgetInjector implements IXposedHookLoadPackage {

    private static final String MODULE_PACKAGE_NAME = "crk.creeperbox";
    
    // Use volatile to ensure visibility across threads
    private static volatile boolean isInjectionTriggered = false;
    private static Context targetContext = null;

    @Override
    public void handleLoadPackage(final XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {
        // 注入所有勾选的应用
        if (lpparam.isFirstApplication) {
            XposedBridge.log("FridaInjector: ✅ Targeting package: " + lpparam.packageName);
            android.util.Log.i("FridaInjector", "✅ Targeting package: " + lpparam.packageName);

            try {
                XposedHelpers.findAndHookMethod(Application.class, "onCreate", new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        super.afterHookedMethod(param);
                        
                        if (targetContext == null) {
                            Application app = (Application) param.thisObject;
                            targetContext = app.getApplicationContext();
                            XposedBridge.log("FridaInjector: ✅ Captured Application Context");
                            android.util.Log.i("FridaInjector", "✅ Captured Application Context");
                            
                            // Trigger Frida injection
                            injectFridaGadget();
                        }
                    }
                });
            } catch (Throwable t) {
                XposedBridge.log("FridaInjector: ❌ Failed to hook Application.onCreate: " + t.getMessage());
                android.util.Log.e("FridaInjector", "❌ Failed to hook Application.onCreate", t);
            }
        }
    }

    private synchronized void injectFridaGadget() {
        if (isInjectionTriggered) {
            XposedBridge.log("FridaInjector: Injection already triggered. Skipping.");
            return;
        }
        
        isInjectionTriggered = true;

        // Run injection in a background thread
        new Thread(new Runnable() {
            @Override
            public void run() {
                if (targetContext == null) {
                    XposedBridge.log("FridaInjector: ❌ Critical Error: Context is null!");
                    android.util.Log.e("FridaInjector", "❌ Critical Error: Context is null!");
                    return;
                }

                try {
                    android.util.Log.i("FridaInjector", "🚀 Starting Frida Gadget injection...");
                    
                    // Get the ApplicationInfo for our own module to find its native library directory
                    ApplicationInfo moduleAppInfo;
                    try {
                        moduleAppInfo = targetContext.getPackageManager().getApplicationInfo(MODULE_PACKAGE_NAME, 0);
                    } catch (PackageManager.NameNotFoundException e) {
                        String errorMsg = "❌ Could not find our own module's package info! Is MODULE_PACKAGE_NAME correct?";
                        android.util.Log.e("FridaInjector", errorMsg, e);
                        XposedBridge.log("FridaInjector: " + errorMsg + " " + e.getMessage());
                        return;
                    }

                    // Build the absolute path to libfrida-gadget.so
                    String fridaSoPath = moduleAppInfo.nativeLibraryDir + File.separator + "libfrida-gadget.so";
                    android.util.Log.i("FridaInjector", "📦 Module native library dir: " + moduleAppInfo.nativeLibraryDir);
                    android.util.Log.i("FridaInjector", "📦 Frida Gadget SO path: " + fridaSoPath);

                    // Verify the SO file exists
                    File fridaSoFile = new File(fridaSoPath);
                    if (!fridaSoFile.exists()) {
                        String errorMsg = "❌ libfrida-gadget.so does not exist at path: " + fridaSoPath;
                        android.util.Log.e("FridaInjector", errorMsg);
                        XposedBridge.log("FridaInjector: " + errorMsg);
                        
                        // Try ARM32 version as fallback
                        String arm32Path = fridaSoPath.replace("arm64", "armeabi-v7a");
                        File arm32File = new File(arm32Path);
                        if (arm32File.exists()) {
                            android.util.Log.i("FridaInjector", "📦 Found ARM32 version, using: " + arm32Path);
                            fridaSoPath = arm32Path;
                        } else {
                            android.util.Log.e("FridaInjector", "❌ Neither ARM64 nor ARM32 version found!");
                            return;
                        }
                    } else {
                        android.util.Log.i("FridaInjector", "✅ SO file exists, size: " + fridaSoFile.length() + " bytes");
                    }

                    // Load Frida Gadget using absolute path
                    android.util.Log.i("FridaInjector", "📦 Loading Frida Gadget from absolute path: " + fridaSoPath);
                    System.load(fridaSoPath);
                    
                    XposedBridge.log("FridaInjector: ✅ Frida Gadget injection successful!");
                    android.util.Log.i("FridaInjector", "✅ Frida Gadget injection successful!");
                    
                } catch (Throwable t) {
                    XposedBridge.log("FridaInjector: ❌ Injection failed: " + t.getMessage());
                    android.util.Log.e("FridaInjector", "❌ Injection failed", t);
                }
            }
        }).start();
    }
}
