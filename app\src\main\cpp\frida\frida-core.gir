<?xml version='1.0' encoding='utf-8'?>
<repository xmlns="http://www.gtk.org/introspection/core/1.0" xmlns:c="http://www.gtk.org/introspection/c/1.0" xmlns:glib="http://www.gtk.org/introspection/glib/1.0" version="1.2">
  <include name="GObject" version="2.0" />
  <include name="Gio" version="2.0" />
  <include name="GLib" version="2.0" />
  <package name="frida-core" />
  <c:include name="frida-core.h" />
  <namespace name="Frida" version="1.0" c:prefix="Frida" c:identifier-prefixes="Frida" c:symbol-prefixes="frida">
    <class name="DeviceManager" c:type="FridaDeviceManager" c:symbol-prefix="device_manager" glib:type-name="FridaDeviceManager" glib:get-type="frida_device_manager_get_type" glib:type-struct="DeviceManagerClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_device_manager_new">
        <return-value transfer-ownership="full">
          <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
        </return-value>
      </constructor>
      <constructor name="with_nonlocal_backends_only" c:identifier="frida_device_manager_new_with_nonlocal_backends_only">
        <return-value transfer-ownership="full">
          <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
        </return-value>
      </constructor>
      <constructor name="with_socket_backend_only" c:identifier="frida_device_manager_new_with_socket_backend_only">
        <return-value transfer-ownership="full">
          <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
        </return-value>
      </constructor>
      <method name="close" c:identifier="frida_device_manager_close">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="close_finish" c:identifier="frida_device_manager_close_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="close_sync" c:identifier="frida_device_manager_close_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_device_by_id" c:identifier="frida_device_manager_get_device_by_id">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="timeout" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_device_by_id_finish" c:identifier="frida_device_manager_get_device_by_id_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_device_by_id_sync" c:identifier="frida_device_manager_get_device_by_id_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="timeout" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_device_by_type" c:identifier="frida_device_manager_get_device_by_type">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="type" transfer-ownership="none">
            <type name="Frida.DeviceType" c:type="FridaDeviceType" />
          </parameter>
          <parameter name="timeout" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_device_by_type_finish" c:identifier="frida_device_manager_get_device_by_type_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_device_by_type_sync" c:identifier="frida_device_manager_get_device_by_type_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="type" transfer-ownership="none">
            <type name="Frida.DeviceType" c:type="FridaDeviceType" />
          </parameter>
          <parameter name="timeout" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_device" c:identifier="frida_device_manager_get_device">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="predicate" transfer-ownership="none" closure="1" scope="call">
            <type name="Frida.DeviceManagerPredicate" c:type="FridaDeviceManagerPredicate" />
          </parameter>
          <parameter name="predicate_target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
          <parameter name="timeout" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="5" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_device_finish" c:identifier="frida_device_manager_get_device_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_device_sync" c:identifier="frida_device_manager_get_device_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="predicate" transfer-ownership="none" closure="1" scope="call">
            <type name="Frida.DeviceManagerPredicate" c:type="FridaDeviceManagerPredicate" />
          </parameter>
          <parameter name="predicate_target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
          <parameter name="timeout" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_device_by_id" c:identifier="frida_device_manager_find_device_by_id">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="timeout" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_device_by_id_finish" c:identifier="frida_device_manager_find_device_by_id_finish" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_device_by_id_sync" c:identifier="frida_device_manager_find_device_by_id_sync" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="timeout" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_device_by_type" c:identifier="frida_device_manager_find_device_by_type">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="type" transfer-ownership="none">
            <type name="Frida.DeviceType" c:type="FridaDeviceType" />
          </parameter>
          <parameter name="timeout" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_device_by_type_finish" c:identifier="frida_device_manager_find_device_by_type_finish" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_device_by_type_sync" c:identifier="frida_device_manager_find_device_by_type_sync" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="type" transfer-ownership="none">
            <type name="Frida.DeviceType" c:type="FridaDeviceType" />
          </parameter>
          <parameter name="timeout" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_device" c:identifier="frida_device_manager_find_device">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="predicate" transfer-ownership="none" closure="1" scope="call">
            <type name="Frida.DeviceManagerPredicate" c:type="FridaDeviceManagerPredicate" />
          </parameter>
          <parameter name="predicate_target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
          <parameter name="timeout" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="5" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_device_finish" c:identifier="frida_device_manager_find_device_finish" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_device_sync" c:identifier="frida_device_manager_find_device_sync" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="predicate" transfer-ownership="none" closure="1" scope="call">
            <type name="Frida.DeviceManagerPredicate" c:type="FridaDeviceManagerPredicate" />
          </parameter>
          <parameter name="predicate_target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
          <parameter name="timeout" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_devices" c:identifier="frida_device_manager_enumerate_devices">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_devices_finish" c:identifier="frida_device_manager_enumerate_devices_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.DeviceList" c:type="FridaDeviceList*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_devices_sync" c:identifier="frida_device_manager_enumerate_devices_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.DeviceList" c:type="FridaDeviceList*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="add_remote_device" c:identifier="frida_device_manager_add_remote_device">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="address" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.RemoteDeviceOptions" c:type="FridaRemoteDeviceOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="add_remote_device_finish" c:identifier="frida_device_manager_add_remote_device_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="add_remote_device_sync" c:identifier="frida_device_manager_add_remote_device_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="address" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.RemoteDeviceOptions" c:type="FridaRemoteDeviceOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="remove_remote_device" c:identifier="frida_device_manager_remove_remote_device">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="address" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="remove_remote_device_finish" c:identifier="frida_device_manager_remove_remote_device_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="remove_remote_device_sync" c:identifier="frida_device_manager_remove_remote_device_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </instance-parameter>
          <parameter name="address" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <glib:signal name="added">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="device" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="removed">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="device" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="changed">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
      </glib:signal>
      <callback name="Predicate" c:type="FridaDeviceManagerPredicate">
        <return-value transfer-ownership="full">
          <type name="gboolean" c:type="gboolean" />
        </return-value>
        <parameters>
          <parameter name="device" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </parameter>
          <parameter name="user_data" transfer-ownership="none" closure="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </callback>
    </class>
    <class name="DeviceList" c:type="FridaDeviceList" c:symbol-prefix="device_list" glib:type-name="FridaDeviceList" glib:get-type="frida_device_list_get_type" glib:type-struct="DeviceListClass" parent="GObject.Object" final="1">
      <method name="size" c:identifier="frida_device_list_size">
        <return-value transfer-ownership="full">
          <type name="gint" c:type="gint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceList" c:type="FridaDeviceList*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="get" c:identifier="frida_device_list_get">
        <return-value transfer-ownership="full">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.DeviceList" c:type="FridaDeviceList*" />
          </instance-parameter>
          <parameter name="index" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="Device" c:type="FridaDevice" c:symbol-prefix="device" glib:type-name="FridaDevice" glib:get-type="frida_device_get_type" glib:type-struct="DeviceClass" parent="GObject.Object" final="1">
      <method name="is_lost" c:identifier="frida_device_is_lost">
        <return-value transfer-ownership="full">
          <type name="gboolean" c:type="gboolean" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="query_system_parameters" c:identifier="frida_device_query_system_parameters">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="query_system_parameters_finish" c:identifier="frida_device_query_system_parameters_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="GLib.HashTable" c:type="GHashTable*">
            <type name="utf8" c:type="gchar*" />
            <type name="GLib.Variant" c:type="GVariant*" />
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="query_system_parameters_sync" c:identifier="frida_device_query_system_parameters_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="GLib.HashTable" c:type="GHashTable*">
            <type name="utf8" c:type="gchar*" />
            <type name="GLib.Variant" c:type="GVariant*" />
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_frontmost_application" c:identifier="frida_device_get_frontmost_application">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.FrontmostQueryOptions" c:type="FridaFrontmostQueryOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_frontmost_application_finish" c:identifier="frida_device_get_frontmost_application_finish" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Application" c:type="FridaApplication*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_frontmost_application_sync" c:identifier="frida_device_get_frontmost_application_sync" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Application" c:type="FridaApplication*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.FrontmostQueryOptions" c:type="FridaFrontmostQueryOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_applications" c:identifier="frida_device_enumerate_applications">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ApplicationQueryOptions" c:type="FridaApplicationQueryOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_applications_finish" c:identifier="frida_device_enumerate_applications_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.ApplicationList" c:type="FridaApplicationList*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_applications_sync" c:identifier="frida_device_enumerate_applications_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.ApplicationList" c:type="FridaApplicationList*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ApplicationQueryOptions" c:type="FridaApplicationQueryOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_process_by_pid" c:identifier="frida_device_get_process_by_pid">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_process_by_pid_finish" c:identifier="frida_device_get_process_by_pid_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_process_by_pid_sync" c:identifier="frida_device_get_process_by_pid_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_process_by_name" c:identifier="frida_device_get_process_by_name">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="name" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_process_by_name_finish" c:identifier="frida_device_get_process_by_name_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_process_by_name_sync" c:identifier="frida_device_get_process_by_name_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="name" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_process" c:identifier="frida_device_get_process">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="predicate" transfer-ownership="none" closure="1" scope="call">
            <type name="Frida.DeviceProcessPredicate" c:type="FridaDeviceProcessPredicate" />
          </parameter>
          <parameter name="predicate_target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="5" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_process_finish" c:identifier="frida_device_get_process_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="get_process_sync" c:identifier="frida_device_get_process_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="predicate" transfer-ownership="none" closure="1" scope="call">
            <type name="Frida.DeviceProcessPredicate" c:type="FridaDeviceProcessPredicate" />
          </parameter>
          <parameter name="predicate_target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_process_by_pid" c:identifier="frida_device_find_process_by_pid">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_process_by_pid_finish" c:identifier="frida_device_find_process_by_pid_finish" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_process_by_pid_sync" c:identifier="frida_device_find_process_by_pid_sync" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_process_by_name" c:identifier="frida_device_find_process_by_name">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="name" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_process_by_name_finish" c:identifier="frida_device_find_process_by_name_finish" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_process_by_name_sync" c:identifier="frida_device_find_process_by_name_sync" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="name" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_process" c:identifier="frida_device_find_process">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="predicate" transfer-ownership="none" closure="1" scope="call">
            <type name="Frida.DeviceProcessPredicate" c:type="FridaDeviceProcessPredicate" />
          </parameter>
          <parameter name="predicate_target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="5" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_process_finish" c:identifier="frida_device_find_process_finish" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="find_process_sync" c:identifier="frida_device_find_process_sync" throws="1">
        <return-value transfer-ownership="full" nullable="1">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="predicate" transfer-ownership="none" closure="1" scope="call">
            <type name="Frida.DeviceProcessPredicate" c:type="FridaDeviceProcessPredicate" />
          </parameter>
          <parameter name="predicate_target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_processes" c:identifier="frida_device_enumerate_processes">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessQueryOptions" c:type="FridaProcessQueryOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_processes_finish" c:identifier="frida_device_enumerate_processes_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.ProcessList" c:type="FridaProcessList*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_processes_sync" c:identifier="frida_device_enumerate_processes_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.ProcessList" c:type="FridaProcessList*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ProcessQueryOptions" c:type="FridaProcessQueryOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="enable_spawn_gating" c:identifier="frida_device_enable_spawn_gating">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="enable_spawn_gating_finish" c:identifier="frida_device_enable_spawn_gating_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="enable_spawn_gating_sync" c:identifier="frida_device_enable_spawn_gating_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="disable_spawn_gating" c:identifier="frida_device_disable_spawn_gating">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="disable_spawn_gating_finish" c:identifier="frida_device_disable_spawn_gating_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="disable_spawn_gating_sync" c:identifier="frida_device_disable_spawn_gating_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_pending_spawn" c:identifier="frida_device_enumerate_pending_spawn">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_pending_spawn_finish" c:identifier="frida_device_enumerate_pending_spawn_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.SpawnList" c:type="FridaSpawnList*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_pending_spawn_sync" c:identifier="frida_device_enumerate_pending_spawn_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.SpawnList" c:type="FridaSpawnList*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_pending_children" c:identifier="frida_device_enumerate_pending_children">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_pending_children_finish" c:identifier="frida_device_enumerate_pending_children_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.ChildList" c:type="FridaChildList*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_pending_children_sync" c:identifier="frida_device_enumerate_pending_children_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.ChildList" c:type="FridaChildList*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="spawn" c:identifier="frida_device_spawn">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="program" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="spawn_finish" c:identifier="frida_device_spawn_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="spawn_sync" c:identifier="frida_device_spawn_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="program" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="input" c:identifier="frida_device_input">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="input_finish" c:identifier="frida_device_input_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="input_sync" c:identifier="frida_device_input_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="resume" c:identifier="frida_device_resume">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="resume_finish" c:identifier="frida_device_resume_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="resume_sync" c:identifier="frida_device_resume_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="kill" c:identifier="frida_device_kill">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="kill_finish" c:identifier="frida_device_kill_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="kill_sync" c:identifier="frida_device_kill_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="attach" c:identifier="frida_device_attach">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.SessionOptions" c:type="FridaSessionOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="attach_finish" c:identifier="frida_device_attach_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Session" c:type="FridaSession*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="attach_sync" c:identifier="frida_device_attach_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Session" c:type="FridaSession*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.SessionOptions" c:type="FridaSessionOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="inject_library_file" c:identifier="frida_device_inject_library_file">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="path" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="6" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="inject_library_file_finish" c:identifier="frida_device_inject_library_file_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="inject_library_file_sync" c:identifier="frida_device_inject_library_file_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="path" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="inject_library_blob" c:identifier="frida_device_inject_library_blob">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="blob" transfer-ownership="none">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="6" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="inject_library_blob_finish" c:identifier="frida_device_inject_library_blob_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="inject_library_blob_sync" c:identifier="frida_device_inject_library_blob_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="blob" transfer-ownership="none">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="open_channel" c:identifier="frida_device_open_channel">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="address" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="open_channel_finish" c:identifier="frida_device_open_channel_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Gio.IOStream" c:type="GIOStream*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="open_channel_sync" c:identifier="frida_device_open_channel_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Gio.IOStream" c:type="GIOStream*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="address" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="open_service" c:identifier="frida_device_open_service">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="address" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="open_service_finish" c:identifier="frida_device_open_service_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Service" c:type="FridaService*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="open_service_sync" c:identifier="frida_device_open_service_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Service" c:type="FridaService*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="address" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="unpair" c:identifier="frida_device_unpair">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="unpair_finish" c:identifier="frida_device_unpair_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="unpair_sync" c:identifier="frida_device_unpair_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <property name="id">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_id" c:identifier="frida_device_get_id">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="name">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_name" c:identifier="frida_device_get_name">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="icon" writable="1" construct-only="1">
        <type name="GLib.Variant" c:type="GVariant*" />
      </property>
      <method name="get_icon" c:identifier="frida_device_get_icon">
        <return-value transfer-ownership="none" nullable="1">
          <type name="GLib.Variant" c:type="GVariant*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="dtype">
        <type name="Frida.DeviceType" c:type="FridaDeviceType" />
      </property>
      <method name="get_dtype" c:identifier="frida_device_get_dtype">
        <return-value transfer-ownership="none">
          <type name="Frida.DeviceType" c:type="FridaDeviceType" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="bus">
        <type name="Frida.Bus" c:type="FridaBus*" />
      </property>
      <method name="get_bus" c:identifier="frida_device_get_bus">
        <return-value transfer-ownership="none">
          <type name="Frida.Bus" c:type="FridaBus*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </instance-parameter>
        </parameters>
      </method>
      <glib:signal name="spawn-added">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="spawn" transfer-ownership="none">
            <type name="Frida.Spawn" c:type="FridaSpawn*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="spawn-removed">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="spawn" transfer-ownership="none">
            <type name="Frida.Spawn" c:type="FridaSpawn*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="child-added">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="child" transfer-ownership="none">
            <type name="Frida.Child" c:type="FridaChild*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="child-removed">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="child" transfer-ownership="none">
            <type name="Frida.Child" c:type="FridaChild*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="process-crashed">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="crash" transfer-ownership="none">
            <type name="Frida.Crash" c:type="FridaCrash*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="output">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="fd" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="uninjected">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="lost">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
      </glib:signal>
      <callback name="ProcessPredicate" c:type="FridaDeviceProcessPredicate">
        <return-value transfer-ownership="full">
          <type name="gboolean" c:type="gboolean" />
        </return-value>
        <parameters>
          <parameter name="process" transfer-ownership="none">
            <type name="Frida.Process" c:type="FridaProcess*" />
          </parameter>
          <parameter name="user_data" transfer-ownership="none" closure="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </callback>
    </class>
    <class name="RemoteDeviceOptions" c:type="FridaRemoteDeviceOptions" c:symbol-prefix="remote_device_options" glib:type-name="FridaRemoteDeviceOptions" glib:get-type="frida_remote_device_options_get_type" glib:type-struct="RemoteDeviceOptionsClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_remote_device_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.RemoteDeviceOptions" c:type="FridaRemoteDeviceOptions*" />
        </return-value>
      </constructor>
      <property name="certificate" writable="1">
        <type name="Gio.TlsCertificate" c:type="GTlsCertificate*" />
      </property>
      <method name="get_certificate" c:identifier="frida_remote_device_options_get_certificate">
        <return-value transfer-ownership="none" nullable="1">
          <type name="Gio.TlsCertificate" c:type="GTlsCertificate*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RemoteDeviceOptions" c:type="FridaRemoteDeviceOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_certificate" c:identifier="frida_remote_device_options_set_certificate">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RemoteDeviceOptions" c:type="FridaRemoteDeviceOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="Gio.TlsCertificate" c:type="GTlsCertificate*" />
          </parameter>
        </parameters>
      </method>
      <property name="origin" writable="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_origin" c:identifier="frida_remote_device_options_get_origin">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RemoteDeviceOptions" c:type="FridaRemoteDeviceOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_origin" c:identifier="frida_remote_device_options_set_origin">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RemoteDeviceOptions" c:type="FridaRemoteDeviceOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <property name="token" writable="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_token" c:identifier="frida_remote_device_options_get_token">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RemoteDeviceOptions" c:type="FridaRemoteDeviceOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_token" c:identifier="frida_remote_device_options_set_token">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RemoteDeviceOptions" c:type="FridaRemoteDeviceOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <property name="keepalive-interval" writable="1">
        <type name="gint" c:type="gint" />
      </property>
      <method name="get_keepalive_interval" c:identifier="frida_remote_device_options_get_keepalive_interval">
        <return-value transfer-ownership="none">
          <type name="gint" c:type="gint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RemoteDeviceOptions" c:type="FridaRemoteDeviceOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_keepalive_interval" c:identifier="frida_remote_device_options_set_keepalive_interval">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RemoteDeviceOptions" c:type="FridaRemoteDeviceOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="ApplicationList" c:type="FridaApplicationList" c:symbol-prefix="application_list" glib:type-name="FridaApplicationList" glib:get-type="frida_application_list_get_type" glib:type-struct="ApplicationListClass" parent="GObject.Object" final="1">
      <method name="size" c:identifier="frida_application_list_size">
        <return-value transfer-ownership="full">
          <type name="gint" c:type="gint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ApplicationList" c:type="FridaApplicationList*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="get" c:identifier="frida_application_list_get">
        <return-value transfer-ownership="full">
          <type name="Frida.Application" c:type="FridaApplication*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ApplicationList" c:type="FridaApplicationList*" />
          </instance-parameter>
          <parameter name="index" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="Application" c:type="FridaApplication" c:symbol-prefix="application" glib:type-name="FridaApplication" glib:get-type="frida_application_get_type" glib:type-struct="ApplicationClass" parent="GObject.Object" final="1">
      <property name="identifier" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_identifier" c:identifier="frida_application_get_identifier">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Application" c:type="FridaApplication*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="name" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_name" c:identifier="frida_application_get_name">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Application" c:type="FridaApplication*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="pid" writable="1" construct-only="1">
        <type name="guint" c:type="guint" />
      </property>
      <method name="get_pid" c:identifier="frida_application_get_pid">
        <return-value transfer-ownership="none">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Application" c:type="FridaApplication*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="parameters" writable="1" construct-only="1">
        <type name="GLib.HashTable" c:type="GHashTable*">
          <type name="utf8" c:type="gchar*" />
          <type name="GLib.Variant" c:type="GVariant*" />
        </type>
      </property>
      <method name="get_parameters" c:identifier="frida_application_get_parameters">
        <return-value transfer-ownership="none">
          <type name="GLib.HashTable" c:type="GHashTable*">
            <type name="utf8" c:type="gchar*" />
            <type name="GLib.Variant" c:type="GVariant*" />
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Application" c:type="FridaApplication*" />
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="ProcessList" c:type="FridaProcessList" c:symbol-prefix="process_list" glib:type-name="FridaProcessList" glib:get-type="frida_process_list_get_type" glib:type-struct="ProcessListClass" parent="GObject.Object" final="1">
      <method name="size" c:identifier="frida_process_list_size">
        <return-value transfer-ownership="full">
          <type name="gint" c:type="gint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ProcessList" c:type="FridaProcessList*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="get" c:identifier="frida_process_list_get">
        <return-value transfer-ownership="full">
          <type name="Frida.Process" c:type="FridaProcess*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ProcessList" c:type="FridaProcessList*" />
          </instance-parameter>
          <parameter name="index" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="Process" c:type="FridaProcess" c:symbol-prefix="process" glib:type-name="FridaProcess" glib:get-type="frida_process_get_type" glib:type-struct="ProcessClass" parent="GObject.Object" final="1">
      <property name="pid" writable="1" construct-only="1">
        <type name="guint" c:type="guint" />
      </property>
      <method name="get_pid" c:identifier="frida_process_get_pid">
        <return-value transfer-ownership="none">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Process" c:type="FridaProcess*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="name" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_name" c:identifier="frida_process_get_name">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Process" c:type="FridaProcess*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="parameters" writable="1" construct-only="1">
        <type name="GLib.HashTable" c:type="GHashTable*">
          <type name="utf8" c:type="gchar*" />
          <type name="GLib.Variant" c:type="GVariant*" />
        </type>
      </property>
      <method name="get_parameters" c:identifier="frida_process_get_parameters">
        <return-value transfer-ownership="none">
          <type name="GLib.HashTable" c:type="GHashTable*">
            <type name="utf8" c:type="gchar*" />
            <type name="GLib.Variant" c:type="GVariant*" />
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Process" c:type="FridaProcess*" />
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="ProcessMatchOptions" c:type="FridaProcessMatchOptions" c:symbol-prefix="process_match_options" glib:type-name="FridaProcessMatchOptions" glib:get-type="frida_process_match_options_get_type" glib:type-struct="ProcessMatchOptionsClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_process_match_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
        </return-value>
      </constructor>
      <property name="timeout" writable="1">
        <type name="gint" c:type="gint" />
      </property>
      <method name="get_timeout" c:identifier="frida_process_match_options_get_timeout">
        <return-value transfer-ownership="none">
          <type name="gint" c:type="gint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_timeout" c:identifier="frida_process_match_options_set_timeout">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
        </parameters>
      </method>
      <property name="scope" writable="1">
        <type name="Frida.Scope" c:type="FridaScope" />
      </property>
      <method name="get_scope" c:identifier="frida_process_match_options_get_scope">
        <return-value transfer-ownership="none">
          <type name="Frida.Scope" c:type="FridaScope" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_scope" c:identifier="frida_process_match_options_set_scope">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ProcessMatchOptions" c:type="FridaProcessMatchOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.Scope" c:type="FridaScope" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="SpawnOptions" c:type="FridaSpawnOptions" c:symbol-prefix="spawn_options" glib:type-name="FridaSpawnOptions" glib:get-type="frida_spawn_options_get_type" glib:type-struct="SpawnOptionsClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_spawn_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
        </return-value>
      </constructor>
      <property name="argv" writable="1">
        <array c:type="gchar**">
          <type name="utf8" c:type="gchar*" />
        </array>
      </property>
      <method name="get_argv" c:identifier="frida_spawn_options_get_argv">
        <return-value transfer-ownership="none" nullable="1">
          <array length="0" c:type="gchar**">
            <type name="utf8" c:type="gchar*" />
          </array>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </instance-parameter>
          <parameter name="result_length1" direction="out" transfer-ownership="none">
            <type name="gint" c:type="gint*" />
          </parameter>
        </parameters>
      </method>
      <method name="set_argv" c:identifier="frida_spawn_options_set_argv">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <array length="1" c:type="gchar**">
              <type name="utf8" c:type="gchar*" />
            </array>
          </parameter>
          <parameter name="value_length1" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
        </parameters>
      </method>
      <property name="envp" writable="1">
        <array c:type="gchar**">
          <type name="utf8" c:type="gchar*" />
        </array>
      </property>
      <method name="get_envp" c:identifier="frida_spawn_options_get_envp">
        <return-value transfer-ownership="none" nullable="1">
          <array length="0" c:type="gchar**">
            <type name="utf8" c:type="gchar*" />
          </array>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </instance-parameter>
          <parameter name="result_length1" direction="out" transfer-ownership="none">
            <type name="gint" c:type="gint*" />
          </parameter>
        </parameters>
      </method>
      <method name="set_envp" c:identifier="frida_spawn_options_set_envp">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <array length="1" c:type="gchar**">
              <type name="utf8" c:type="gchar*" />
            </array>
          </parameter>
          <parameter name="value_length1" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
        </parameters>
      </method>
      <property name="env" writable="1">
        <array c:type="gchar**">
          <type name="utf8" c:type="gchar*" />
        </array>
      </property>
      <method name="get_env" c:identifier="frida_spawn_options_get_env">
        <return-value transfer-ownership="none" nullable="1">
          <array length="0" c:type="gchar**">
            <type name="utf8" c:type="gchar*" />
          </array>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </instance-parameter>
          <parameter name="result_length1" direction="out" transfer-ownership="none">
            <type name="gint" c:type="gint*" />
          </parameter>
        </parameters>
      </method>
      <method name="set_env" c:identifier="frida_spawn_options_set_env">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <array length="1" c:type="gchar**">
              <type name="utf8" c:type="gchar*" />
            </array>
          </parameter>
          <parameter name="value_length1" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
        </parameters>
      </method>
      <property name="cwd" writable="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_cwd" c:identifier="frida_spawn_options_get_cwd">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_cwd" c:identifier="frida_spawn_options_set_cwd">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <property name="stdio" writable="1">
        <type name="Frida.Stdio" c:type="FridaStdio" />
      </property>
      <method name="get_stdio" c:identifier="frida_spawn_options_get_stdio">
        <return-value transfer-ownership="none">
          <type name="Frida.Stdio" c:type="FridaStdio" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_stdio" c:identifier="frida_spawn_options_set_stdio">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.Stdio" c:type="FridaStdio" />
          </parameter>
        </parameters>
      </method>
      <property name="aux" writable="1">
        <type name="GLib.HashTable" c:type="GHashTable*">
          <type name="utf8" c:type="gchar*" />
          <type name="GLib.Variant" c:type="GVariant*" />
        </type>
      </property>
      <method name="get_aux" c:identifier="frida_spawn_options_get_aux">
        <return-value transfer-ownership="none">
          <type name="GLib.HashTable" c:type="GHashTable*">
            <type name="utf8" c:type="gchar*" />
            <type name="GLib.Variant" c:type="GVariant*" />
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_aux" c:identifier="frida_spawn_options_set_aux">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnOptions" c:type="FridaSpawnOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="GLib.HashTable" c:type="GHashTable*">
              <type name="utf8" c:type="gchar*" />
              <type name="GLib.Variant" c:type="GVariant*" />
            </type>
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="SpawnList" c:type="FridaSpawnList" c:symbol-prefix="spawn_list" glib:type-name="FridaSpawnList" glib:get-type="frida_spawn_list_get_type" glib:type-struct="SpawnListClass" parent="GObject.Object" final="1">
      <method name="size" c:identifier="frida_spawn_list_size">
        <return-value transfer-ownership="full">
          <type name="gint" c:type="gint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnList" c:type="FridaSpawnList*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="get" c:identifier="frida_spawn_list_get">
        <return-value transfer-ownership="full">
          <type name="Frida.Spawn" c:type="FridaSpawn*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SpawnList" c:type="FridaSpawnList*" />
          </instance-parameter>
          <parameter name="index" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="Spawn" c:type="FridaSpawn" c:symbol-prefix="spawn" glib:type-name="FridaSpawn" glib:get-type="frida_spawn_get_type" glib:type-struct="SpawnClass" parent="GObject.Object" final="1">
      <property name="pid" writable="1" construct-only="1">
        <type name="guint" c:type="guint" />
      </property>
      <method name="get_pid" c:identifier="frida_spawn_get_pid">
        <return-value transfer-ownership="none">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Spawn" c:type="FridaSpawn*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="identifier" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_identifier" c:identifier="frida_spawn_get_identifier">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Spawn" c:type="FridaSpawn*" />
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="ChildList" c:type="FridaChildList" c:symbol-prefix="child_list" glib:type-name="FridaChildList" glib:get-type="frida_child_list_get_type" glib:type-struct="ChildListClass" parent="GObject.Object" final="1">
      <method name="size" c:identifier="frida_child_list_size">
        <return-value transfer-ownership="full">
          <type name="gint" c:type="gint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ChildList" c:type="FridaChildList*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="get" c:identifier="frida_child_list_get">
        <return-value transfer-ownership="full">
          <type name="Frida.Child" c:type="FridaChild*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ChildList" c:type="FridaChildList*" />
          </instance-parameter>
          <parameter name="index" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="Child" c:type="FridaChild" c:symbol-prefix="child" glib:type-name="FridaChild" glib:get-type="frida_child_get_type" glib:type-struct="ChildClass" parent="GObject.Object" final="1">
      <property name="pid" writable="1" construct-only="1">
        <type name="guint" c:type="guint" />
      </property>
      <method name="get_pid" c:identifier="frida_child_get_pid">
        <return-value transfer-ownership="none">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Child" c:type="FridaChild*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="parent-pid" writable="1" construct-only="1">
        <type name="guint" c:type="guint" />
      </property>
      <method name="get_parent_pid" c:identifier="frida_child_get_parent_pid">
        <return-value transfer-ownership="none">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Child" c:type="FridaChild*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="origin" writable="1" construct-only="1">
        <type name="Frida.ChildOrigin" c:type="FridaChildOrigin" />
      </property>
      <method name="get_origin" c:identifier="frida_child_get_origin">
        <return-value transfer-ownership="none">
          <type name="Frida.ChildOrigin" c:type="FridaChildOrigin" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Child" c:type="FridaChild*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="identifier" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_identifier" c:identifier="frida_child_get_identifier">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Child" c:type="FridaChild*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="path" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_path" c:identifier="frida_child_get_path">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Child" c:type="FridaChild*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="argv" writable="1" construct-only="1">
        <array c:type="gchar**">
          <type name="utf8" c:type="gchar*" />
        </array>
      </property>
      <method name="get_argv" c:identifier="frida_child_get_argv">
        <return-value transfer-ownership="none" nullable="1">
          <array length="0" c:type="gchar**">
            <type name="utf8" c:type="gchar*" />
          </array>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Child" c:type="FridaChild*" />
          </instance-parameter>
          <parameter name="result_length1" direction="out" transfer-ownership="none">
            <type name="gint" c:type="gint*" />
          </parameter>
        </parameters>
      </method>
      <property name="envp" writable="1" construct-only="1">
        <array c:type="gchar**">
          <type name="utf8" c:type="gchar*" />
        </array>
      </property>
      <method name="get_envp" c:identifier="frida_child_get_envp">
        <return-value transfer-ownership="none" nullable="1">
          <array length="0" c:type="gchar**">
            <type name="utf8" c:type="gchar*" />
          </array>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Child" c:type="FridaChild*" />
          </instance-parameter>
          <parameter name="result_length1" direction="out" transfer-ownership="none">
            <type name="gint" c:type="gint*" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="Crash" c:type="FridaCrash" c:symbol-prefix="crash" glib:type-name="FridaCrash" glib:get-type="frida_crash_get_type" glib:type-struct="CrashClass" parent="GObject.Object" final="1">
      <property name="pid" writable="1" construct-only="1">
        <type name="guint" c:type="guint" />
      </property>
      <method name="get_pid" c:identifier="frida_crash_get_pid">
        <return-value transfer-ownership="none">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Crash" c:type="FridaCrash*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="process-name" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_process_name" c:identifier="frida_crash_get_process_name">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Crash" c:type="FridaCrash*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="summary" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_summary" c:identifier="frida_crash_get_summary">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Crash" c:type="FridaCrash*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="report" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_report" c:identifier="frida_crash_get_report">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Crash" c:type="FridaCrash*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="parameters" writable="1" construct-only="1">
        <type name="GLib.HashTable" c:type="GHashTable*">
          <type name="utf8" c:type="gchar*" />
          <type name="GLib.Variant" c:type="GVariant*" />
        </type>
      </property>
      <method name="get_parameters" c:identifier="frida_crash_get_parameters">
        <return-value transfer-ownership="none">
          <type name="GLib.HashTable" c:type="GHashTable*">
            <type name="utf8" c:type="gchar*" />
            <type name="GLib.Variant" c:type="GVariant*" />
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Crash" c:type="FridaCrash*" />
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="Bus" c:type="FridaBus" c:symbol-prefix="bus" glib:type-name="FridaBus" glib:get-type="frida_bus_get_type" glib:type-struct="BusClass" parent="GObject.Object" final="1">
      <method name="is_detached" c:identifier="frida_bus_is_detached">
        <return-value transfer-ownership="full">
          <type name="gboolean" c:type="gboolean" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Bus" c:type="FridaBus*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="attach" c:identifier="frida_bus_attach">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Bus" c:type="FridaBus*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="attach_finish" c:identifier="frida_bus_attach_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Bus" c:type="FridaBus*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="attach_sync" c:identifier="frida_bus_attach_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Bus" c:type="FridaBus*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="post" c:identifier="frida_bus_post">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Bus" c:type="FridaBus*" />
          </instance-parameter>
          <parameter name="json" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none" nullable="1">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
        </parameters>
      </method>
      <glib:signal name="detached">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
      </glib:signal>
      <glib:signal name="message">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="json" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none" nullable="1">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
        </parameters>
      </glib:signal>
    </class>
    <class name="Service" c:type="FridaService" c:symbol-prefix="service" glib:type-name="FridaService" glib:get-type="frida_service_get_type" glib:type-struct="ServiceClass" parent="GObject.Object" final="1">
      <method name="is_closed" c:identifier="frida_service_is_closed">
        <return-value transfer-ownership="full">
          <type name="gboolean" c:type="gboolean" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Service" c:type="FridaService*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="activate" c:identifier="frida_service_activate">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Service" c:type="FridaService*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="activate_finish" c:identifier="frida_service_activate_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Service" c:type="FridaService*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="activate_sync" c:identifier="frida_service_activate_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Service" c:type="FridaService*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="cancel" c:identifier="frida_service_cancel">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Service" c:type="FridaService*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="cancel_finish" c:identifier="frida_service_cancel_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Service" c:type="FridaService*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="cancel_sync" c:identifier="frida_service_cancel_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Service" c:type="FridaService*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="request" c:identifier="frida_service_request">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Service" c:type="FridaService*" />
          </instance-parameter>
          <parameter name="parameters" transfer-ownership="none">
            <type name="GLib.Variant" c:type="GVariant*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="request_finish" c:identifier="frida_service_request_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="GLib.Variant" c:type="GVariant*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Service" c:type="FridaService*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="request_sync" c:identifier="frida_service_request_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="GLib.Variant" c:type="GVariant*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Service" c:type="FridaService*" />
          </instance-parameter>
          <parameter name="parameters" transfer-ownership="none">
            <type name="GLib.Variant" c:type="GVariant*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <glib:signal name="close">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
      </glib:signal>
      <glib:signal name="message">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="message" transfer-ownership="none">
            <type name="GLib.Variant" c:type="GVariant*" />
          </parameter>
        </parameters>
      </glib:signal>
    </class>
    <class name="Session" c:type="FridaSession" c:symbol-prefix="session" glib:type-name="FridaSession" glib:get-type="frida_session_get_type" glib:type-struct="SessionClass" parent="GObject.Object" final="1">
      <method name="is_detached" c:identifier="frida_session_is_detached">
        <return-value transfer-ownership="full">
          <type name="gboolean" c:type="gboolean" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="detach" c:identifier="frida_session_detach">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="detach_finish" c:identifier="frida_session_detach_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="detach_sync" c:identifier="frida_session_detach_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="resume" c:identifier="frida_session_resume">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="resume_finish" c:identifier="frida_session_resume_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="resume_sync" c:identifier="frida_session_resume_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="enable_child_gating" c:identifier="frida_session_enable_child_gating">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="enable_child_gating_finish" c:identifier="frida_session_enable_child_gating_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="enable_child_gating_sync" c:identifier="frida_session_enable_child_gating_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="disable_child_gating" c:identifier="frida_session_disable_child_gating">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="disable_child_gating_finish" c:identifier="frida_session_disable_child_gating_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="disable_child_gating_sync" c:identifier="frida_session_disable_child_gating_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="create_script" c:identifier="frida_session_create_script">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="source" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="create_script_finish" c:identifier="frida_session_create_script_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Script" c:type="FridaScript*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="create_script_sync" c:identifier="frida_session_create_script_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Script" c:type="FridaScript*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="source" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="create_script_from_bytes" c:identifier="frida_session_create_script_from_bytes">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="bytes" transfer-ownership="none">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="create_script_from_bytes_finish" c:identifier="frida_session_create_script_from_bytes_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Script" c:type="FridaScript*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="create_script_from_bytes_sync" c:identifier="frida_session_create_script_from_bytes_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.Script" c:type="FridaScript*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="bytes" transfer-ownership="none">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="compile_script" c:identifier="frida_session_compile_script">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="source" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="compile_script_finish" c:identifier="frida_session_compile_script_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="GLib.Bytes" c:type="GBytes*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="compile_script_sync" c:identifier="frida_session_compile_script_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="GLib.Bytes" c:type="GBytes*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="source" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="snapshot_script" c:identifier="frida_session_snapshot_script">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="embed_script" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.SnapshotOptions" c:type="FridaSnapshotOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="snapshot_script_finish" c:identifier="frida_session_snapshot_script_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="GLib.Bytes" c:type="GBytes*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="snapshot_script_sync" c:identifier="frida_session_snapshot_script_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="GLib.Bytes" c:type="GBytes*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="embed_script" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.SnapshotOptions" c:type="FridaSnapshotOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="setup_peer_connection" c:identifier="frida_session_setup_peer_connection">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.PeerOptions" c:type="FridaPeerOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="setup_peer_connection_finish" c:identifier="frida_session_setup_peer_connection_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="setup_peer_connection_sync" c:identifier="frida_session_setup_peer_connection_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.PeerOptions" c:type="FridaPeerOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="join_portal" c:identifier="frida_session_join_portal">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="address" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.PortalOptions" c:type="FridaPortalOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="join_portal_finish" c:identifier="frida_session_join_portal_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.PortalMembership" c:type="FridaPortalMembership*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="join_portal_sync" c:identifier="frida_session_join_portal_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.PortalMembership" c:type="FridaPortalMembership*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
          <parameter name="address" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.PortalOptions" c:type="FridaPortalOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <property name="pid" writable="1" construct-only="1">
        <type name="guint" c:type="guint" />
      </property>
      <method name="get_pid" c:identifier="frida_session_get_pid">
        <return-value transfer-ownership="none">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="persist-timeout" writable="1" construct-only="1">
        <type name="guint" c:type="guint" />
      </property>
      <method name="get_persist_timeout" c:identifier="frida_session_get_persist_timeout">
        <return-value transfer-ownership="none">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Session" c:type="FridaSession*" />
          </instance-parameter>
        </parameters>
      </method>
      <glib:signal name="detached">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="reason" transfer-ownership="none">
            <type name="Frida.SessionDetachReason" c:type="FridaSessionDetachReason" />
          </parameter>
          <parameter name="crash" transfer-ownership="none" nullable="1">
            <type name="Frida.Crash" c:type="FridaCrash*" />
          </parameter>
        </parameters>
      </glib:signal>
    </class>
    <class name="Script" c:type="FridaScript" c:symbol-prefix="script" glib:type-name="FridaScript" glib:get-type="frida_script_get_type" glib:type-struct="ScriptClass" parent="GObject.Object" final="1">
      <method name="is_destroyed" c:identifier="frida_script_is_destroyed">
        <return-value transfer-ownership="full">
          <type name="gboolean" c:type="gboolean" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="load" c:identifier="frida_script_load">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="load_finish" c:identifier="frida_script_load_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="load_sync" c:identifier="frida_script_load_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="unload" c:identifier="frida_script_unload">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="unload_finish" c:identifier="frida_script_unload_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="unload_sync" c:identifier="frida_script_unload_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="eternalize" c:identifier="frida_script_eternalize">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="eternalize_finish" c:identifier="frida_script_eternalize_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="eternalize_sync" c:identifier="frida_script_eternalize_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="post" c:identifier="frida_script_post">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="json" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none" nullable="1">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
        </parameters>
      </method>
      <method name="enable_debugger" c:identifier="frida_script_enable_debugger">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="port" transfer-ownership="none">
            <type name="guint16" c:type="guint16" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="enable_debugger_finish" c:identifier="frida_script_enable_debugger_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="enable_debugger_sync" c:identifier="frida_script_enable_debugger_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="port" transfer-ownership="none">
            <type name="guint16" c:type="guint16" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="disable_debugger" c:identifier="frida_script_disable_debugger">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="disable_debugger_finish" c:identifier="frida_script_disable_debugger_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="disable_debugger_sync" c:identifier="frida_script_disable_debugger_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Script" c:type="FridaScript*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <glib:signal name="destroyed">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
      </glib:signal>
      <glib:signal name="message">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="json" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none" nullable="1">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
        </parameters>
      </glib:signal>
    </class>
    <class name="PortalMembership" c:type="FridaPortalMembership" c:symbol-prefix="portal_membership" glib:type-name="FridaPortalMembership" glib:get-type="frida_portal_membership_get_type" glib:type-struct="PortalMembershipClass" parent="GObject.Object" final="1">
      <method name="terminate" c:identifier="frida_portal_membership_terminate">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalMembership" c:type="FridaPortalMembership*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="terminate_finish" c:identifier="frida_portal_membership_terminate_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalMembership" c:type="FridaPortalMembership*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="terminate_sync" c:identifier="frida_portal_membership_terminate_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalMembership" c:type="FridaPortalMembership*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="PackageManager" c:type="FridaPackageManager" c:symbol-prefix="package_manager" glib:type-name="FridaPackageManager" glib:get-type="frida_package_manager_get_type" glib:type-struct="PackageManagerClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_package_manager_new">
        <return-value transfer-ownership="full">
          <type name="Frida.PackageManager" c:type="FridaPackageManager*" />
        </return-value>
      </constructor>
      <method name="search" c:identifier="frida_package_manager_search">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageManager" c:type="FridaPackageManager*" />
          </instance-parameter>
          <parameter name="query" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.PackageSearchOptions" c:type="FridaPackageSearchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="search_finish" c:identifier="frida_package_manager_search_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.PackageSearchResult" c:type="FridaPackageSearchResult*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageManager" c:type="FridaPackageManager*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="search_sync" c:identifier="frida_package_manager_search_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.PackageSearchResult" c:type="FridaPackageSearchResult*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageManager" c:type="FridaPackageManager*" />
          </instance-parameter>
          <parameter name="query" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.PackageSearchOptions" c:type="FridaPackageSearchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="install" c:identifier="frida_package_manager_install">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageManager" c:type="FridaPackageManager*" />
          </instance-parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.PackageInstallOptions" c:type="FridaPackageInstallOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="install_finish" c:identifier="frida_package_manager_install_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.PackageInstallResult" c:type="FridaPackageInstallResult*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageManager" c:type="FridaPackageManager*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="install_sync" c:identifier="frida_package_manager_install_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.PackageInstallResult" c:type="FridaPackageInstallResult*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageManager" c:type="FridaPackageManager*" />
          </instance-parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.PackageInstallOptions" c:type="FridaPackageInstallOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <property name="registry" writable="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_registry" c:identifier="frida_package_manager_get_registry">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageManager" c:type="FridaPackageManager*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_registry" c:identifier="frida_package_manager_set_registry">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageManager" c:type="FridaPackageManager*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <glib:signal name="install-progress">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="phase" transfer-ownership="none">
            <type name="Frida.PackageInstallPhase" c:type="FridaPackageInstallPhase" />
          </parameter>
          <parameter name="fraction" transfer-ownership="none">
            <type name="gdouble" c:type="gdouble" />
          </parameter>
          <parameter name="details" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </glib:signal>
    </class>
    <class name="Package" c:type="FridaPackage" c:symbol-prefix="package" glib:type-name="FridaPackage" glib:get-type="frida_package_get_type" glib:type-struct="PackageClass" parent="GObject.Object" final="1">
      <property name="name" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_name" c:identifier="frida_package_get_name">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Package" c:type="FridaPackage*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="version" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_version" c:identifier="frida_package_get_version">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Package" c:type="FridaPackage*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="description" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_description" c:identifier="frida_package_get_description">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Package" c:type="FridaPackage*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="url" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_url" c:identifier="frida_package_get_url">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Package" c:type="FridaPackage*" />
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="PackageList" c:type="FridaPackageList" c:symbol-prefix="package_list" glib:type-name="FridaPackageList" glib:get-type="frida_package_list_get_type" glib:type-struct="PackageListClass" parent="GObject.Object" final="1">
      <method name="size" c:identifier="frida_package_list_size">
        <return-value transfer-ownership="full">
          <type name="gint" c:type="gint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageList" c:type="FridaPackageList*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="get" c:identifier="frida_package_list_get">
        <return-value transfer-ownership="full">
          <type name="Frida.Package" c:type="FridaPackage*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageList" c:type="FridaPackageList*" />
          </instance-parameter>
          <parameter name="index" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="PackageSearchOptions" c:type="FridaPackageSearchOptions" c:symbol-prefix="package_search_options" glib:type-name="FridaPackageSearchOptions" glib:get-type="frida_package_search_options_get_type" glib:type-struct="PackageSearchOptionsClass" parent="GObject.Object">
      <constructor name="new" c:identifier="frida_package_search_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.PackageSearchOptions" c:type="FridaPackageSearchOptions*" />
        </return-value>
      </constructor>
      <property name="offset" writable="1">
        <type name="guint" c:type="guint" />
      </property>
      <method name="get_offset" c:identifier="frida_package_search_options_get_offset">
        <return-value transfer-ownership="none">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageSearchOptions" c:type="FridaPackageSearchOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_offset" c:identifier="frida_package_search_options_set_offset">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageSearchOptions" c:type="FridaPackageSearchOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
        </parameters>
      </method>
      <property name="limit" writable="1">
        <type name="guint" c:type="guint" />
      </property>
      <method name="get_limit" c:identifier="frida_package_search_options_get_limit">
        <return-value transfer-ownership="none">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageSearchOptions" c:type="FridaPackageSearchOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_limit" c:identifier="frida_package_search_options_set_limit">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageSearchOptions" c:type="FridaPackageSearchOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="PackageSearchResult" c:type="FridaPackageSearchResult" c:symbol-prefix="package_search_result" glib:type-name="FridaPackageSearchResult" glib:get-type="frida_package_search_result_get_type" glib:type-struct="PackageSearchResultClass" parent="GObject.Object" final="1">
      <property name="packages" writable="1" construct-only="1">
        <type name="Frida.PackageList" c:type="FridaPackageList*" />
      </property>
      <method name="get_packages" c:identifier="frida_package_search_result_get_packages">
        <return-value transfer-ownership="none">
          <type name="Frida.PackageList" c:type="FridaPackageList*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageSearchResult" c:type="FridaPackageSearchResult*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="total" writable="1" construct-only="1">
        <type name="guint" c:type="guint" />
      </property>
      <method name="get_total" c:identifier="frida_package_search_result_get_total">
        <return-value transfer-ownership="none">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageSearchResult" c:type="FridaPackageSearchResult*" />
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="PackageInstallOptions" c:type="FridaPackageInstallOptions" c:symbol-prefix="package_install_options" glib:type-name="FridaPackageInstallOptions" glib:get-type="frida_package_install_options_get_type" glib:type-struct="PackageInstallOptionsClass" parent="GObject.Object">
      <method name="clear_specs" c:identifier="frida_package_install_options_clear_specs">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageInstallOptions" c:type="FridaPackageInstallOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="add_spec" c:identifier="frida_package_install_options_add_spec">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageInstallOptions" c:type="FridaPackageInstallOptions*" />
          </instance-parameter>
          <parameter name="spec" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <method name="clear_omits" c:identifier="frida_package_install_options_clear_omits">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageInstallOptions" c:type="FridaPackageInstallOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="add_omit" c:identifier="frida_package_install_options_add_omit">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageInstallOptions" c:type="FridaPackageInstallOptions*" />
          </instance-parameter>
          <parameter name="role" transfer-ownership="none">
            <type name="Frida.PackageRole" c:type="FridaPackageRole" />
          </parameter>
        </parameters>
      </method>
      <constructor name="new" c:identifier="frida_package_install_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.PackageInstallOptions" c:type="FridaPackageInstallOptions*" />
        </return-value>
      </constructor>
      <property name="project-root" writable="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_project_root" c:identifier="frida_package_install_options_get_project_root">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageInstallOptions" c:type="FridaPackageInstallOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_project_root" c:identifier="frida_package_install_options_set_project_root">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageInstallOptions" c:type="FridaPackageInstallOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <property name="role" writable="1">
        <type name="Frida.PackageRole" c:type="FridaPackageRole" />
      </property>
      <method name="get_role" c:identifier="frida_package_install_options_get_role">
        <return-value transfer-ownership="none">
          <type name="Frida.PackageRole" c:type="FridaPackageRole" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageInstallOptions" c:type="FridaPackageInstallOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_role" c:identifier="frida_package_install_options_set_role">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageInstallOptions" c:type="FridaPackageInstallOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.PackageRole" c:type="FridaPackageRole" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="PackageInstallResult" c:type="FridaPackageInstallResult" c:symbol-prefix="package_install_result" glib:type-name="FridaPackageInstallResult" glib:get-type="frida_package_install_result_get_type" glib:type-struct="PackageInstallResultClass" parent="GObject.Object" final="1">
      <property name="packages" writable="1" construct-only="1">
        <type name="Frida.PackageList" c:type="FridaPackageList*" />
      </property>
      <method name="get_packages" c:identifier="frida_package_install_result_get_packages">
        <return-value transfer-ownership="none">
          <type name="Frida.PackageList" c:type="FridaPackageList*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PackageInstallResult" c:type="FridaPackageInstallResult*" />
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="ControlService" c:type="FridaControlService" c:symbol-prefix="control_service" glib:type-name="FridaControlService" glib:get-type="frida_control_service_get_type" glib:type-struct="ControlServiceClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_control_service_new" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.ControlService" c:type="FridaControlService*" />
        </return-value>
        <parameters>
          <parameter name="endpoint_params" transfer-ownership="none">
            <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ControlServiceOptions" c:type="FridaControlServiceOptions*" />
          </parameter>
        </parameters>
      </constructor>
      <constructor name="with_device" c:identifier="frida_control_service_new_with_device" throws="1">
        <return-value transfer-ownership="full">
          <type name="Frida.ControlService" c:type="FridaControlService*" />
        </return-value>
        <parameters>
          <parameter name="device" transfer-ownership="none">
            <type name="Frida.Device" c:type="FridaDevice*" />
          </parameter>
          <parameter name="endpoint_params" transfer-ownership="none">
            <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.ControlServiceOptions" c:type="FridaControlServiceOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </constructor>
      <method name="start" c:identifier="frida_control_service_start">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlService" c:type="FridaControlService*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="start_finish" c:identifier="frida_control_service_start_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlService" c:type="FridaControlService*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="start_sync" c:identifier="frida_control_service_start_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlService" c:type="FridaControlService*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="stop" c:identifier="frida_control_service_stop">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlService" c:type="FridaControlService*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="stop_finish" c:identifier="frida_control_service_stop_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlService" c:type="FridaControlService*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="stop_sync" c:identifier="frida_control_service_stop_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlService" c:type="FridaControlService*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <property name="endpoint-params" writable="1" construct-only="1">
        <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
      </property>
      <method name="get_endpoint_params" c:identifier="frida_control_service_get_endpoint_params">
        <return-value transfer-ownership="none">
          <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlService" c:type="FridaControlService*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="options" writable="1" construct-only="1">
        <type name="Frida.ControlServiceOptions" c:type="FridaControlServiceOptions*" />
      </property>
      <method name="get_options" c:identifier="frida_control_service_get_options">
        <return-value transfer-ownership="none">
          <type name="Frida.ControlServiceOptions" c:type="FridaControlServiceOptions*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlService" c:type="FridaControlService*" />
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="ControlServiceOptions" c:type="FridaControlServiceOptions" c:symbol-prefix="control_service_options" glib:type-name="FridaControlServiceOptions" glib:get-type="frida_control_service_options_get_type" glib:type-struct="ControlServiceOptionsClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_control_service_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.ControlServiceOptions" c:type="FridaControlServiceOptions*" />
        </return-value>
      </constructor>
      <property name="sysroot" writable="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_sysroot" c:identifier="frida_control_service_options_get_sysroot">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlServiceOptions" c:type="FridaControlServiceOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_sysroot" c:identifier="frida_control_service_options_set_sysroot">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlServiceOptions" c:type="FridaControlServiceOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <property name="enable-preload" writable="1">
        <type name="gboolean" c:type="gboolean" />
      </property>
      <method name="get_enable_preload" c:identifier="frida_control_service_options_get_enable_preload">
        <return-value transfer-ownership="none">
          <type name="gboolean" c:type="gboolean" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlServiceOptions" c:type="FridaControlServiceOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_enable_preload" c:identifier="frida_control_service_options_set_enable_preload">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlServiceOptions" c:type="FridaControlServiceOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="gboolean" c:type="gboolean" />
          </parameter>
        </parameters>
      </method>
      <property name="report-crashes" writable="1">
        <type name="gboolean" c:type="gboolean" />
      </property>
      <method name="get_report_crashes" c:identifier="frida_control_service_options_get_report_crashes">
        <return-value transfer-ownership="none">
          <type name="gboolean" c:type="gboolean" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlServiceOptions" c:type="FridaControlServiceOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_report_crashes" c:identifier="frida_control_service_options_set_report_crashes">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ControlServiceOptions" c:type="FridaControlServiceOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="gboolean" c:type="gboolean" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="PortalService" c:type="FridaPortalService" c:symbol-prefix="portal_service" glib:type-name="FridaPortalService" glib:get-type="frida_portal_service_get_type" glib:type-struct="PortalServiceClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_portal_service_new">
        <return-value transfer-ownership="full">
          <type name="Frida.PortalService" c:type="FridaPortalService*" />
        </return-value>
        <parameters>
          <parameter name="cluster_params" transfer-ownership="none">
            <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
          </parameter>
          <parameter name="control_params" transfer-ownership="none" nullable="1">
            <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
          </parameter>
        </parameters>
      </constructor>
      <method name="start" c:identifier="frida_portal_service_start">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="start_finish" c:identifier="frida_portal_service_start_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="start_sync" c:identifier="frida_portal_service_start_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="stop" c:identifier="frida_portal_service_stop">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="stop_finish" c:identifier="frida_portal_service_stop_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="stop_sync" c:identifier="frida_portal_service_stop_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="kick" c:identifier="frida_portal_service_kick">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
        </parameters>
      </method>
      <method name="post" c:identifier="frida_portal_service_post">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="json" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none" nullable="1">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
        </parameters>
      </method>
      <method name="narrowcast" c:identifier="frida_portal_service_narrowcast">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="tag" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="json" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none" nullable="1">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
        </parameters>
      </method>
      <method name="broadcast" c:identifier="frida_portal_service_broadcast">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="json" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none" nullable="1">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_tags" c:identifier="frida_portal_service_enumerate_tags">
        <return-value transfer-ownership="full" nullable="1">
          <array length="1" c:type="gchar**">
            <type name="utf8" c:type="gchar*" />
          </array>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="result_length1" direction="out" transfer-ownership="none">
            <type name="gint" c:type="gint*" />
          </parameter>
        </parameters>
      </method>
      <method name="tag" c:identifier="frida_portal_service_tag">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="tag" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <method name="untag" c:identifier="frida_portal_service_untag">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="tag" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <property name="device">
        <type name="Frida.Device" c:type="FridaDevice*" />
      </property>
      <method name="get_device" c:identifier="frida_portal_service_get_device">
        <return-value transfer-ownership="none">
          <type name="Frida.Device" c:type="FridaDevice*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="cluster-params" writable="1" construct-only="1">
        <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
      </property>
      <method name="get_cluster_params" c:identifier="frida_portal_service_get_cluster_params">
        <return-value transfer-ownership="none">
          <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="control-params" writable="1" construct-only="1">
        <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
      </property>
      <method name="get_control_params" c:identifier="frida_portal_service_get_control_params">
        <return-value transfer-ownership="none" nullable="1">
          <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalService" c:type="FridaPortalService*" />
          </instance-parameter>
        </parameters>
      </method>
      <glib:signal name="node-connected">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="remote_address" transfer-ownership="none">
            <type name="Gio.SocketAddress" c:type="GSocketAddress*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="node-joined">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="application" transfer-ownership="none">
            <type name="Frida.Application" c:type="FridaApplication*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="node-left">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="application" transfer-ownership="none">
            <type name="Frida.Application" c:type="FridaApplication*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="node-disconnected">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="remote_address" transfer-ownership="none">
            <type name="Gio.SocketAddress" c:type="GSocketAddress*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="controller-connected">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="remote_address" transfer-ownership="none">
            <type name="Gio.SocketAddress" c:type="GSocketAddress*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="controller-disconnected">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="remote_address" transfer-ownership="none">
            <type name="Gio.SocketAddress" c:type="GSocketAddress*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="authenticated">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="session_info" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="subscribe">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="message">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="connection_id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="json" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none" nullable="1">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
        </parameters>
      </glib:signal>
    </class>
    <class name="FileMonitor" c:type="FridaFileMonitor" c:symbol-prefix="file_monitor" glib:type-name="FridaFileMonitor" glib:get-type="frida_file_monitor_get_type" glib:type-struct="FileMonitorClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_file_monitor_new">
        <return-value transfer-ownership="full">
          <type name="Frida.FileMonitor" c:type="FridaFileMonitor*" />
        </return-value>
        <parameters>
          <parameter name="path" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </constructor>
      <method name="enable" c:identifier="frida_file_monitor_enable">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.FileMonitor" c:type="FridaFileMonitor*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="enable_finish" c:identifier="frida_file_monitor_enable_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.FileMonitor" c:type="FridaFileMonitor*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="enable_sync" c:identifier="frida_file_monitor_enable_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.FileMonitor" c:type="FridaFileMonitor*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="disable" c:identifier="frida_file_monitor_disable">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.FileMonitor" c:type="FridaFileMonitor*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="disable_finish" c:identifier="frida_file_monitor_disable_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.FileMonitor" c:type="FridaFileMonitor*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="disable_sync" c:identifier="frida_file_monitor_disable_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.FileMonitor" c:type="FridaFileMonitor*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <property name="path" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_path" c:identifier="frida_file_monitor_get_path">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.FileMonitor" c:type="FridaFileMonitor*" />
          </instance-parameter>
        </parameters>
      </method>
      <glib:signal name="change">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="file_path" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="other_file_path" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="event" transfer-ownership="none">
            <type name="Gio.FileMonitorEvent" c:type="GFileMonitorEvent" />
          </parameter>
        </parameters>
      </glib:signal>
    </class>
    <class name="Compiler" c:type="FridaCompiler" c:symbol-prefix="compiler" glib:type-name="FridaCompiler" glib:get-type="frida_compiler_get_type" glib:type-struct="CompilerClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_compiler_new">
        <return-value transfer-ownership="full">
          <type name="Frida.Compiler" c:type="FridaCompiler*" />
        </return-value>
        <parameters>
          <parameter name="manager" transfer-ownership="none" nullable="1">
            <type name="Frida.DeviceManager" c:type="FridaDeviceManager*" />
          </parameter>
        </parameters>
      </constructor>
      <method name="build" c:identifier="frida_compiler_build">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Compiler" c:type="FridaCompiler*" />
          </instance-parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.BuildOptions" c:type="FridaBuildOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="build_finish" c:identifier="frida_compiler_build_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="utf8" c:type="gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Compiler" c:type="FridaCompiler*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="build_sync" c:identifier="frida_compiler_build_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="utf8" c:type="gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Compiler" c:type="FridaCompiler*" />
          </instance-parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.BuildOptions" c:type="FridaBuildOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="watch" c:identifier="frida_compiler_watch">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Compiler" c:type="FridaCompiler*" />
          </instance-parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.WatchOptions" c:type="FridaWatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="watch_finish" c:identifier="frida_compiler_watch_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Compiler" c:type="FridaCompiler*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="watch_sync" c:identifier="frida_compiler_watch_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Compiler" c:type="FridaCompiler*" />
          </instance-parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="options" transfer-ownership="none" nullable="1">
            <type name="Frida.WatchOptions" c:type="FridaWatchOptions*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="schedule_on_frida_thread" c:identifier="frida_compiler_schedule_on_frida_thread">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Compiler" c:type="FridaCompiler*" />
          </instance-parameter>
          <parameter name="function" transfer-ownership="none" closure="1" scope="notified" destroy="2">
            <type name="GLib.SourceFunc" c:type="GSourceFunc" />
          </parameter>
          <parameter name="function_target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
          <parameter name="function_target_destroy_notify" transfer-ownership="none" scope="call">
            <type name="GLib.DestroyNotify" c:type="GDestroyNotify" />
          </parameter>
        </parameters>
      </method>
      <glib:signal name="starting">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
      </glib:signal>
      <glib:signal name="finished">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
      </glib:signal>
      <glib:signal name="output">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="bundle" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </glib:signal>
      <glib:signal name="diagnostics">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="diagnostics" transfer-ownership="none">
            <type name="GLib.Variant" c:type="GVariant*" />
          </parameter>
        </parameters>
      </glib:signal>
    </class>
    <class name="CompilerOptions" c:type="FridaCompilerOptions" c:symbol-prefix="compiler_options" glib:type-name="FridaCompilerOptions" glib:get-type="frida_compiler_options_get_type" glib:type-struct="CompilerOptionsClass" parent="GObject.Object">
      <constructor name="new" c:identifier="frida_compiler_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
        </return-value>
      </constructor>
      <property name="project-root" writable="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_project_root" c:identifier="frida_compiler_options_get_project_root">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_project_root" c:identifier="frida_compiler_options_set_project_root">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <property name="output-format" writable="1">
        <type name="Frida.OutputFormat" c:type="FridaOutputFormat" />
      </property>
      <method name="get_output_format" c:identifier="frida_compiler_options_get_output_format">
        <return-value transfer-ownership="none">
          <type name="Frida.OutputFormat" c:type="FridaOutputFormat" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_output_format" c:identifier="frida_compiler_options_set_output_format">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.OutputFormat" c:type="FridaOutputFormat" />
          </parameter>
        </parameters>
      </method>
      <property name="bundle-format" writable="1">
        <type name="Frida.BundleFormat" c:type="FridaBundleFormat" />
      </property>
      <method name="get_bundle_format" c:identifier="frida_compiler_options_get_bundle_format">
        <return-value transfer-ownership="none">
          <type name="Frida.BundleFormat" c:type="FridaBundleFormat" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_bundle_format" c:identifier="frida_compiler_options_set_bundle_format">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.BundleFormat" c:type="FridaBundleFormat" />
          </parameter>
        </parameters>
      </method>
      <property name="type-check" writable="1">
        <type name="Frida.TypeCheckMode" c:type="FridaTypeCheckMode" />
      </property>
      <method name="get_type_check" c:identifier="frida_compiler_options_get_type_check">
        <return-value transfer-ownership="none">
          <type name="Frida.TypeCheckMode" c:type="FridaTypeCheckMode" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_type_check" c:identifier="frida_compiler_options_set_type_check">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.TypeCheckMode" c:type="FridaTypeCheckMode" />
          </parameter>
        </parameters>
      </method>
      <property name="source-maps" writable="1">
        <type name="Frida.SourceMaps" c:type="FridaSourceMaps" />
      </property>
      <method name="get_source_maps" c:identifier="frida_compiler_options_get_source_maps">
        <return-value transfer-ownership="none">
          <type name="Frida.SourceMaps" c:type="FridaSourceMaps" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_source_maps" c:identifier="frida_compiler_options_set_source_maps">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.SourceMaps" c:type="FridaSourceMaps" />
          </parameter>
        </parameters>
      </method>
      <property name="compression" writable="1">
        <type name="Frida.JsCompression" c:type="FridaJsCompression" />
      </property>
      <method name="get_compression" c:identifier="frida_compiler_options_get_compression">
        <return-value transfer-ownership="none">
          <type name="Frida.JsCompression" c:type="FridaJsCompression" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_compression" c:identifier="frida_compiler_options_set_compression">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.CompilerOptions" c:type="FridaCompilerOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.JsCompression" c:type="FridaJsCompression" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="BuildOptions" c:type="FridaBuildOptions" c:symbol-prefix="build_options" glib:type-name="FridaBuildOptions" glib:get-type="frida_build_options_get_type" glib:type-struct="BuildOptionsClass" parent="Frida.CompilerOptions" final="1">
      <constructor name="new" c:identifier="frida_build_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.BuildOptions" c:type="FridaBuildOptions*" />
        </return-value>
      </constructor>
    </class>
    <class name="WatchOptions" c:type="FridaWatchOptions" c:symbol-prefix="watch_options" glib:type-name="FridaWatchOptions" glib:get-type="frida_watch_options_get_type" glib:type-struct="WatchOptionsClass" parent="Frida.CompilerOptions" final="1">
      <constructor name="new" c:identifier="frida_watch_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.WatchOptions" c:type="FridaWatchOptions*" />
        </return-value>
      </constructor>
    </class>
    <class name="StaticAuthenticationService" c:type="FridaStaticAuthenticationService" c:symbol-prefix="static_authentication_service" glib:type-name="FridaStaticAuthenticationService" glib:get-type="frida_static_authentication_service_get_type" glib:type-struct="StaticAuthenticationServiceClass" parent="GObject.Object" final="1">
      <implements name="Frida.AuthenticationService" />
      <constructor name="new" c:identifier="frida_static_authentication_service_new">
        <return-value transfer-ownership="full">
          <type name="Frida.StaticAuthenticationService" c:type="FridaStaticAuthenticationService*" />
        </return-value>
        <parameters>
          <parameter name="token" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </constructor>
      <property name="token-hash" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_token_hash" c:identifier="frida_static_authentication_service_get_token_hash">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.StaticAuthenticationService" c:type="FridaStaticAuthenticationService*" />
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="FrontmostQueryOptions" c:type="FridaFrontmostQueryOptions" c:symbol-prefix="frontmost_query_options" glib:type-name="FridaFrontmostQueryOptions" glib:get-type="frida_frontmost_query_options_get_type" glib:type-struct="FrontmostQueryOptionsClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_frontmost_query_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.FrontmostQueryOptions" c:type="FridaFrontmostQueryOptions*" />
        </return-value>
      </constructor>
      <property name="scope" writable="1">
        <type name="Frida.Scope" c:type="FridaScope" />
      </property>
      <method name="get_scope" c:identifier="frida_frontmost_query_options_get_scope">
        <return-value transfer-ownership="none">
          <type name="Frida.Scope" c:type="FridaScope" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.FrontmostQueryOptions" c:type="FridaFrontmostQueryOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_scope" c:identifier="frida_frontmost_query_options_set_scope">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.FrontmostQueryOptions" c:type="FridaFrontmostQueryOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.Scope" c:type="FridaScope" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="ApplicationQueryOptions" c:type="FridaApplicationQueryOptions" c:symbol-prefix="application_query_options" glib:type-name="FridaApplicationQueryOptions" glib:get-type="frida_application_query_options_get_type" glib:type-struct="ApplicationQueryOptionsClass" parent="GObject.Object" final="1">
      <method name="select_identifier" c:identifier="frida_application_query_options_select_identifier">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ApplicationQueryOptions" c:type="FridaApplicationQueryOptions*" />
          </instance-parameter>
          <parameter name="identifier" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <method name="has_selected_identifiers" c:identifier="frida_application_query_options_has_selected_identifiers">
        <return-value transfer-ownership="full">
          <type name="gboolean" c:type="gboolean" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ApplicationQueryOptions" c:type="FridaApplicationQueryOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="enumerate_selected_identifiers" c:identifier="frida_application_query_options_enumerate_selected_identifiers">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ApplicationQueryOptions" c:type="FridaApplicationQueryOptions*" />
          </instance-parameter>
          <parameter name="func" transfer-ownership="none" closure="1" scope="call">
            <type name="GLib.Func" c:type="GFunc" />
          </parameter>
          <parameter name="func_target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <constructor name="new" c:identifier="frida_application_query_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.ApplicationQueryOptions" c:type="FridaApplicationQueryOptions*" />
        </return-value>
      </constructor>
      <property name="scope" writable="1">
        <type name="Frida.Scope" c:type="FridaScope" />
      </property>
      <method name="get_scope" c:identifier="frida_application_query_options_get_scope">
        <return-value transfer-ownership="none">
          <type name="Frida.Scope" c:type="FridaScope" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ApplicationQueryOptions" c:type="FridaApplicationQueryOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_scope" c:identifier="frida_application_query_options_set_scope">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ApplicationQueryOptions" c:type="FridaApplicationQueryOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.Scope" c:type="FridaScope" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="ProcessQueryOptions" c:type="FridaProcessQueryOptions" c:symbol-prefix="process_query_options" glib:type-name="FridaProcessQueryOptions" glib:get-type="frida_process_query_options_get_type" glib:type-struct="ProcessQueryOptionsClass" parent="GObject.Object" final="1">
      <method name="select_pid" c:identifier="frida_process_query_options_select_pid">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ProcessQueryOptions" c:type="FridaProcessQueryOptions*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
        </parameters>
      </method>
      <method name="has_selected_pids" c:identifier="frida_process_query_options_has_selected_pids">
        <return-value transfer-ownership="full">
          <type name="gboolean" c:type="gboolean" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ProcessQueryOptions" c:type="FridaProcessQueryOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="enumerate_selected_pids" c:identifier="frida_process_query_options_enumerate_selected_pids">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ProcessQueryOptions" c:type="FridaProcessQueryOptions*" />
          </instance-parameter>
          <parameter name="func" transfer-ownership="none" closure="1" scope="call">
            <type name="GLib.Func" c:type="GFunc" />
          </parameter>
          <parameter name="func_target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <constructor name="new" c:identifier="frida_process_query_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.ProcessQueryOptions" c:type="FridaProcessQueryOptions*" />
        </return-value>
      </constructor>
      <property name="scope" writable="1">
        <type name="Frida.Scope" c:type="FridaScope" />
      </property>
      <method name="get_scope" c:identifier="frida_process_query_options_get_scope">
        <return-value transfer-ownership="none">
          <type name="Frida.Scope" c:type="FridaScope" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ProcessQueryOptions" c:type="FridaProcessQueryOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_scope" c:identifier="frida_process_query_options_set_scope">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ProcessQueryOptions" c:type="FridaProcessQueryOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.Scope" c:type="FridaScope" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="SessionOptions" c:type="FridaSessionOptions" c:symbol-prefix="session_options" glib:type-name="FridaSessionOptions" glib:get-type="frida_session_options_get_type" glib:type-struct="SessionOptionsClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_session_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.SessionOptions" c:type="FridaSessionOptions*" />
        </return-value>
      </constructor>
      <property name="realm" writable="1">
        <type name="Frida.Realm" c:type="FridaRealm" />
      </property>
      <method name="get_realm" c:identifier="frida_session_options_get_realm">
        <return-value transfer-ownership="none">
          <type name="Frida.Realm" c:type="FridaRealm" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SessionOptions" c:type="FridaSessionOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_realm" c:identifier="frida_session_options_set_realm">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SessionOptions" c:type="FridaSessionOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.Realm" c:type="FridaRealm" />
          </parameter>
        </parameters>
      </method>
      <property name="persist-timeout" writable="1">
        <type name="guint" c:type="guint" />
      </property>
      <method name="get_persist_timeout" c:identifier="frida_session_options_get_persist_timeout">
        <return-value transfer-ownership="none">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SessionOptions" c:type="FridaSessionOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_persist_timeout" c:identifier="frida_session_options_set_persist_timeout">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SessionOptions" c:type="FridaSessionOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
        </parameters>
      </method>
      <property name="emulated-agent-path" writable="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_emulated_agent_path" c:identifier="frida_session_options_get_emulated_agent_path">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SessionOptions" c:type="FridaSessionOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_emulated_agent_path" c:identifier="frida_session_options_set_emulated_agent_path">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SessionOptions" c:type="FridaSessionOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="ScriptOptions" c:type="FridaScriptOptions" c:symbol-prefix="script_options" glib:type-name="FridaScriptOptions" glib:get-type="frida_script_options_get_type" glib:type-struct="ScriptOptionsClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_script_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
        </return-value>
      </constructor>
      <property name="name" writable="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_name" c:identifier="frida_script_options_get_name">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_name" c:identifier="frida_script_options_set_name">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <property name="snapshot" writable="1">
        <type name="GLib.Bytes" c:type="GBytes*" />
      </property>
      <method name="get_snapshot" c:identifier="frida_script_options_get_snapshot">
        <return-value transfer-ownership="none" nullable="1">
          <type name="GLib.Bytes" c:type="GBytes*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_snapshot" c:identifier="frida_script_options_set_snapshot">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
        </parameters>
      </method>
      <property name="snapshot-transport" writable="1">
        <type name="Frida.SnapshotTransport" c:type="FridaSnapshotTransport" />
      </property>
      <method name="get_snapshot_transport" c:identifier="frida_script_options_get_snapshot_transport">
        <return-value transfer-ownership="none">
          <type name="Frida.SnapshotTransport" c:type="FridaSnapshotTransport" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_snapshot_transport" c:identifier="frida_script_options_set_snapshot_transport">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.SnapshotTransport" c:type="FridaSnapshotTransport" />
          </parameter>
        </parameters>
      </method>
      <property name="runtime" writable="1">
        <type name="Frida.ScriptRuntime" c:type="FridaScriptRuntime" />
      </property>
      <method name="get_runtime" c:identifier="frida_script_options_get_runtime">
        <return-value transfer-ownership="none">
          <type name="Frida.ScriptRuntime" c:type="FridaScriptRuntime" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_runtime" c:identifier="frida_script_options_set_runtime">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.ScriptOptions" c:type="FridaScriptOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.ScriptRuntime" c:type="FridaScriptRuntime" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="SnapshotOptions" c:type="FridaSnapshotOptions" c:symbol-prefix="snapshot_options" glib:type-name="FridaSnapshotOptions" glib:get-type="frida_snapshot_options_get_type" glib:type-struct="SnapshotOptionsClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_snapshot_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.SnapshotOptions" c:type="FridaSnapshotOptions*" />
        </return-value>
      </constructor>
      <property name="warmup-script" writable="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_warmup_script" c:identifier="frida_snapshot_options_get_warmup_script">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SnapshotOptions" c:type="FridaSnapshotOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_warmup_script" c:identifier="frida_snapshot_options_set_warmup_script">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SnapshotOptions" c:type="FridaSnapshotOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <property name="runtime" writable="1">
        <type name="Frida.ScriptRuntime" c:type="FridaScriptRuntime" />
      </property>
      <method name="get_runtime" c:identifier="frida_snapshot_options_get_runtime">
        <return-value transfer-ownership="none">
          <type name="Frida.ScriptRuntime" c:type="FridaScriptRuntime" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SnapshotOptions" c:type="FridaSnapshotOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_runtime" c:identifier="frida_snapshot_options_set_runtime">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.SnapshotOptions" c:type="FridaSnapshotOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none">
            <type name="Frida.ScriptRuntime" c:type="FridaScriptRuntime" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="PortalOptions" c:type="FridaPortalOptions" c:symbol-prefix="portal_options" glib:type-name="FridaPortalOptions" glib:get-type="frida_portal_options_get_type" glib:type-struct="PortalOptionsClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_portal_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.PortalOptions" c:type="FridaPortalOptions*" />
        </return-value>
      </constructor>
      <property name="certificate" writable="1">
        <type name="Gio.TlsCertificate" c:type="GTlsCertificate*" />
      </property>
      <method name="get_certificate" c:identifier="frida_portal_options_get_certificate">
        <return-value transfer-ownership="none" nullable="1">
          <type name="Gio.TlsCertificate" c:type="GTlsCertificate*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalOptions" c:type="FridaPortalOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_certificate" c:identifier="frida_portal_options_set_certificate">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalOptions" c:type="FridaPortalOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="Gio.TlsCertificate" c:type="GTlsCertificate*" />
          </parameter>
        </parameters>
      </method>
      <property name="token" writable="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_token" c:identifier="frida_portal_options_get_token">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalOptions" c:type="FridaPortalOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_token" c:identifier="frida_portal_options_set_token">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalOptions" c:type="FridaPortalOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <property name="acl" writable="1">
        <array c:type="gchar**">
          <type name="utf8" c:type="gchar*" />
        </array>
      </property>
      <method name="get_acl" c:identifier="frida_portal_options_get_acl">
        <return-value transfer-ownership="none" nullable="1">
          <array length="0" c:type="gchar**">
            <type name="utf8" c:type="gchar*" />
          </array>
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalOptions" c:type="FridaPortalOptions*" />
          </instance-parameter>
          <parameter name="result_length1" direction="out" transfer-ownership="none">
            <type name="gint" c:type="gint*" />
          </parameter>
        </parameters>
      </method>
      <method name="set_acl" c:identifier="frida_portal_options_set_acl">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PortalOptions" c:type="FridaPortalOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <array length="1" c:type="gchar**">
              <type name="utf8" c:type="gchar*" />
            </array>
          </parameter>
          <parameter name="value_length1" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="PeerOptions" c:type="FridaPeerOptions" c:symbol-prefix="peer_options" glib:type-name="FridaPeerOptions" glib:get-type="frida_peer_options_get_type" glib:type-struct="PeerOptionsClass" parent="GObject.Object" final="1">
      <method name="clear_relays" c:identifier="frida_peer_options_clear_relays">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PeerOptions" c:type="FridaPeerOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="add_relay" c:identifier="frida_peer_options_add_relay">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PeerOptions" c:type="FridaPeerOptions*" />
          </instance-parameter>
          <parameter name="relay" transfer-ownership="none">
            <type name="Frida.Relay" c:type="FridaRelay*" />
          </parameter>
        </parameters>
      </method>
      <method name="enumerate_relays" c:identifier="frida_peer_options_enumerate_relays">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PeerOptions" c:type="FridaPeerOptions*" />
          </instance-parameter>
          <parameter name="func" transfer-ownership="none" closure="1" scope="call">
            <type name="GLib.Func" c:type="GFunc" />
          </parameter>
          <parameter name="func_target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <constructor name="new" c:identifier="frida_peer_options_new">
        <return-value transfer-ownership="full">
          <type name="Frida.PeerOptions" c:type="FridaPeerOptions*" />
        </return-value>
      </constructor>
      <property name="stun-server" writable="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_stun_server" c:identifier="frida_peer_options_get_stun_server">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PeerOptions" c:type="FridaPeerOptions*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_stun_server" c:identifier="frida_peer_options_set_stun_server">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.PeerOptions" c:type="FridaPeerOptions*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="Relay" c:type="FridaRelay" c:symbol-prefix="relay" glib:type-name="FridaRelay" glib:get-type="frida_relay_get_type" glib:type-struct="RelayClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_relay_new">
        <return-value transfer-ownership="full">
          <type name="Frida.Relay" c:type="FridaRelay*" />
        </return-value>
        <parameters>
          <parameter name="address" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="username" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="password" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="kind" transfer-ownership="none">
            <type name="Frida.RelayKind" c:type="FridaRelayKind" />
          </parameter>
        </parameters>
      </constructor>
      <property name="address" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_address" c:identifier="frida_relay_get_address">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Relay" c:type="FridaRelay*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="username" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_username" c:identifier="frida_relay_get_username">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Relay" c:type="FridaRelay*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="password" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_password" c:identifier="frida_relay_get_password">
        <return-value transfer-ownership="none">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Relay" c:type="FridaRelay*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="kind" writable="1" construct-only="1">
        <type name="Frida.RelayKind" c:type="FridaRelayKind" />
      </property>
      <method name="get_kind" c:identifier="frida_relay_get_kind">
        <return-value transfer-ownership="none">
          <type name="Frida.RelayKind" c:type="FridaRelayKind" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Relay" c:type="FridaRelay*" />
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="RpcClient" c:type="FridaRpcClient" c:symbol-prefix="rpc_client" glib:type-name="FridaRpcClient" glib:get-type="frida_rpc_client_get_type" glib:type-struct="RpcClientClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_rpc_client_new">
        <return-value transfer-ownership="full">
          <type name="Frida.RpcClient" c:type="FridaRpcClient*" />
        </return-value>
        <parameters>
          <parameter name="peer" transfer-ownership="none">
            <type name="Frida.RpcPeer" c:type="FridaRpcPeer*" />
          </parameter>
        </parameters>
      </constructor>
      <method name="call" c:identifier="frida_rpc_client_call">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RpcClient" c:type="FridaRpcClient*" />
          </instance-parameter>
          <parameter name="method" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="args" transfer-ownership="none">
            <array length="2" c:type="JsonNode**">
              <type name="Json.Node" c:type="JsonNode*" />
            </array>
          </parameter>
          <parameter name="args_length1" transfer-ownership="none">
            <type name="gint" c:type="gint" />
          </parameter>
          <parameter name="data" transfer-ownership="none" nullable="1">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="6" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="call_finish" c:identifier="frida_rpc_client_call_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="Json.Node" c:type="JsonNode*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RpcClient" c:type="FridaRpcClient*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <method name="try_handle_message" c:identifier="frida_rpc_client_try_handle_message">
        <return-value transfer-ownership="full">
          <type name="gboolean" c:type="gboolean" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RpcClient" c:type="FridaRpcClient*" />
          </instance-parameter>
          <parameter name="json" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
        </parameters>
      </method>
      <property name="peer" writable="1" construct-only="1">
        <type name="Frida.RpcPeer" c:type="FridaRpcPeer*" />
      </property>
      <method name="get_peer" c:identifier="frida_rpc_client_get_peer">
        <return-value transfer-ownership="none">
          <type name="Frida.RpcPeer" c:type="FridaRpcPeer*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RpcClient" c:type="FridaRpcClient*" />
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="EndpointParameters" c:type="FridaEndpointParameters" c:symbol-prefix="endpoint_parameters" glib:type-name="FridaEndpointParameters" glib:get-type="frida_endpoint_parameters_get_type" glib:type-struct="EndpointParametersClass" parent="GObject.Object" final="1">
      <constructor name="new" c:identifier="frida_endpoint_parameters_new">
        <return-value transfer-ownership="full">
          <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
        </return-value>
        <parameters>
          <parameter name="address" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="port" transfer-ownership="none">
            <type name="guint16" c:type="guint16" />
          </parameter>
          <parameter name="certificate" transfer-ownership="none" nullable="1">
            <type name="Gio.TlsCertificate" c:type="GTlsCertificate*" />
          </parameter>
          <parameter name="origin" transfer-ownership="none" nullable="1">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="auth_service" transfer-ownership="none" nullable="1">
            <type name="Frida.AuthenticationService" c:type="FridaAuthenticationService*" />
          </parameter>
          <parameter name="asset_root" transfer-ownership="none" nullable="1">
            <type name="Gio.File" c:type="GFile*" />
          </parameter>
        </parameters>
      </constructor>
      <property name="address" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_address" c:identifier="frida_endpoint_parameters_get_address">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="port" writable="1" construct-only="1">
        <type name="guint16" c:type="guint16" />
      </property>
      <method name="get_port" c:identifier="frida_endpoint_parameters_get_port">
        <return-value transfer-ownership="none">
          <type name="guint16" c:type="guint16" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="certificate" writable="1" construct-only="1">
        <type name="Gio.TlsCertificate" c:type="GTlsCertificate*" />
      </property>
      <method name="get_certificate" c:identifier="frida_endpoint_parameters_get_certificate">
        <return-value transfer-ownership="none" nullable="1">
          <type name="Gio.TlsCertificate" c:type="GTlsCertificate*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="origin" writable="1" construct-only="1">
        <type name="utf8" c:type="gchar*" />
      </property>
      <method name="get_origin" c:identifier="frida_endpoint_parameters_get_origin">
        <return-value transfer-ownership="none" nullable="1">
          <type name="utf8" c:type="const gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="auth-service" writable="1" construct-only="1">
        <type name="Frida.AuthenticationService" c:type="FridaAuthenticationService*" />
      </property>
      <method name="get_auth_service" c:identifier="frida_endpoint_parameters_get_auth_service">
        <return-value transfer-ownership="none" nullable="1">
          <type name="Frida.AuthenticationService" c:type="FridaAuthenticationService*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
          </instance-parameter>
        </parameters>
      </method>
      <property name="asset-root" writable="1">
        <type name="Gio.File" c:type="GFile*" />
      </property>
      <method name="get_asset_root" c:identifier="frida_endpoint_parameters_get_asset_root">
        <return-value transfer-ownership="none" nullable="1">
          <type name="Gio.File" c:type="GFile*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_asset_root" c:identifier="frida_endpoint_parameters_set_asset_root">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.EndpointParameters" c:type="FridaEndpointParameters*" />
          </instance-parameter>
          <parameter name="value" transfer-ownership="none" nullable="1">
            <type name="Gio.File" c:type="GFile*" />
          </parameter>
        </parameters>
      </method>
    </class>
    <interface name="Injector" c:type="FridaInjector" c:symbol-prefix="injector" glib:type-name="FridaInjector" glib:get-type="frida_injector_get_type" glib:type-struct="InjectorIface">
      <prerequisite name="GObject.Object" />
      <function name="new" c:identifier="frida_injector_new">
        <return-value transfer-ownership="full">
          <type name="Frida.Injector" c:type="FridaInjector*" />
        </return-value>
      </function>
      <function name="new_inprocess" c:identifier="frida_injector_new_inprocess">
        <return-value transfer-ownership="full">
          <type name="Frida.Injector" c:type="FridaInjector*" />
        </return-value>
      </function>
      <method name="close" c:identifier="frida_injector_close">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="close_finish" c:identifier="frida_injector_close_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <virtual-method name="close" invoker="close">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="2" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </virtual-method>
      <virtual-method name="close_finish" invoker="close_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </virtual-method>
      <method name="close_sync" c:identifier="frida_injector_close_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="inject_library_file" c:identifier="frida_injector_inject_library_file">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="path" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="6" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="inject_library_file_finish" c:identifier="frida_injector_inject_library_file_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <virtual-method name="inject_library_file" invoker="inject_library_file">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="path" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="6" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </virtual-method>
      <virtual-method name="inject_library_file_finish" invoker="inject_library_file_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </virtual-method>
      <method name="inject_library_file_sync" c:identifier="frida_injector_inject_library_file_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="path" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="inject_library_blob" c:identifier="frida_injector_inject_library_blob">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="blob" transfer-ownership="none">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="6" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="inject_library_blob_finish" c:identifier="frida_injector_inject_library_blob_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <virtual-method name="inject_library_blob" invoker="inject_library_blob">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="blob" transfer-ownership="none">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="6" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </virtual-method>
      <virtual-method name="inject_library_blob_finish" invoker="inject_library_blob_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </virtual-method>
      <method name="inject_library_blob_sync" c:identifier="frida_injector_inject_library_blob_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="blob" transfer-ownership="none">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
          <parameter name="entrypoint" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="demonitor" c:identifier="frida_injector_demonitor">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="demonitor_finish" c:identifier="frida_injector_demonitor_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <virtual-method name="demonitor" invoker="demonitor">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </virtual-method>
      <virtual-method name="demonitor_finish" invoker="demonitor_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </virtual-method>
      <method name="demonitor_sync" c:identifier="frida_injector_demonitor_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="demonitor_and_clone_state" c:identifier="frida_injector_demonitor_and_clone_state">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="demonitor_and_clone_state_finish" c:identifier="frida_injector_demonitor_and_clone_state_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <virtual-method name="demonitor_and_clone_state" invoker="demonitor_and_clone_state">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </virtual-method>
      <virtual-method name="demonitor_and_clone_state_finish" invoker="demonitor_and_clone_state_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </virtual-method>
      <method name="demonitor_and_clone_state_sync" c:identifier="frida_injector_demonitor_and_clone_state_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="guint" c:type="guint" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <method name="recreate_thread" c:identifier="frida_injector_recreate_thread">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="recreate_thread_finish" c:identifier="frida_injector_recreate_thread_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <virtual-method name="recreate_thread" invoker="recreate_thread">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </virtual-method>
      <virtual-method name="recreate_thread_finish" invoker="recreate_thread_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </virtual-method>
      <method name="recreate_thread_sync" c:identifier="frida_injector_recreate_thread_sync" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.Injector" c:type="FridaInjector*" />
          </instance-parameter>
          <parameter name="pid" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
        </parameters>
      </method>
      <glib:signal name="uninjected">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <parameter name="id" transfer-ownership="none">
            <type name="guint" c:type="guint" />
          </parameter>
        </parameters>
      </glib:signal>
    </interface>
    <interface name="AuthenticationService" c:type="FridaAuthenticationService" c:symbol-prefix="authentication_service" glib:type-name="FridaAuthenticationService" glib:get-type="frida_authentication_service_get_type" glib:type-struct="AuthenticationServiceIface">
      <prerequisite name="GObject.Object" />
      <method name="authenticate" c:identifier="frida_authentication_service_authenticate">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.AuthenticationService" c:type="FridaAuthenticationService*" />
          </instance-parameter>
          <parameter name="token" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="authenticate_finish" c:identifier="frida_authentication_service_authenticate_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="utf8" c:type="gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.AuthenticationService" c:type="FridaAuthenticationService*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <virtual-method name="authenticate" invoker="authenticate">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.AuthenticationService" c:type="FridaAuthenticationService*" />
          </instance-parameter>
          <parameter name="token" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="3" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </virtual-method>
      <virtual-method name="authenticate_finish" invoker="authenticate_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="utf8" c:type="gchar*" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.AuthenticationService" c:type="FridaAuthenticationService*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </virtual-method>
    </interface>
    <interface name="RpcPeer" c:type="FridaRpcPeer" c:symbol-prefix="rpc_peer" glib:type-name="FridaRpcPeer" glib:get-type="frida_rpc_peer_get_type" glib:type-struct="RpcPeerIface">
      <prerequisite name="GObject.Object" />
      <method name="post_rpc_message" c:identifier="frida_rpc_peer_post_rpc_message">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RpcPeer" c:type="FridaRpcPeer*" />
          </instance-parameter>
          <parameter name="json" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none" nullable="1">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </method>
      <method name="post_rpc_message_finish" c:identifier="frida_rpc_peer_post_rpc_message_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RpcPeer" c:type="FridaRpcPeer*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </method>
      <virtual-method name="post_rpc_message" invoker="post_rpc_message">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RpcPeer" c:type="FridaRpcPeer*" />
          </instance-parameter>
          <parameter name="json" transfer-ownership="none">
            <type name="utf8" c:type="const gchar*" />
          </parameter>
          <parameter name="data" transfer-ownership="none" nullable="1">
            <type name="GLib.Bytes" c:type="GBytes*" />
          </parameter>
          <parameter name="cancellable" transfer-ownership="none" nullable="1">
            <type name="Gio.Cancellable" c:type="GCancellable*" />
          </parameter>
          <parameter name="_callback_" transfer-ownership="none" nullable="1" closure="4" scope="async">
            <type name="Gio.AsyncReadyCallback" c:type="GAsyncReadyCallback" />
          </parameter>
          <parameter name="_callback__target" transfer-ownership="none" nullable="1">
            <type name="gpointer" c:type="void*" />
          </parameter>
        </parameters>
      </virtual-method>
      <virtual-method name="post_rpc_message_finish" invoker="post_rpc_message_finish" throws="1">
        <return-value transfer-ownership="full">
          <type name="none" c:type="void" />
        </return-value>
        <parameters>
          <instance-parameter name="self" transfer-ownership="none">
            <type name="Frida.RpcPeer" c:type="FridaRpcPeer*" />
          </instance-parameter>
          <parameter name="_res_" transfer-ownership="none">
            <type name="Gio.AsyncResult" c:type="GAsyncResult*" />
          </parameter>
        </parameters>
      </virtual-method>
    </interface>
    <enumeration name="Runtime" c:type="FridaRuntime" glib:type-name="FridaRuntime" glib:get-type="frida_runtime_get_type">
      <member name="glib" c:identifier="FRIDA_RUNTIME_GLIB" value="0" />
      <member name="other" c:identifier="FRIDA_RUNTIME_OTHER" value="1" />
    </enumeration>
    <enumeration name="DeviceType" c:type="FridaDeviceType" glib:type-name="FridaDeviceType" glib:get-type="frida_device_type_get_type">
      <member name="local" c:identifier="FRIDA_DEVICE_TYPE_LOCAL" value="0" />
      <member name="remote" c:identifier="FRIDA_DEVICE_TYPE_REMOTE" value="1" />
      <member name="usb" c:identifier="FRIDA_DEVICE_TYPE_USB" value="2" />
    </enumeration>
    <enumeration name="PackageInstallPhase" c:type="FridaPackageInstallPhase" glib:type-name="FridaPackageInstallPhase" glib:get-type="frida_package_install_phase_get_type">
      <member name="initializing" c:identifier="FRIDA_PACKAGE_INSTALL_PHASE_INITIALIZING" value="0" />
      <member name="preparing_dependencies" c:identifier="FRIDA_PACKAGE_INSTALL_PHASE_PREPARING_DEPENDENCIES" value="1" />
      <member name="resolving_package" c:identifier="FRIDA_PACKAGE_INSTALL_PHASE_RESOLVING_PACKAGE" value="2" />
      <member name="fetching_resource" c:identifier="FRIDA_PACKAGE_INSTALL_PHASE_FETCHING_RESOURCE" value="3" />
      <member name="package_already_installed" c:identifier="FRIDA_PACKAGE_INSTALL_PHASE_PACKAGE_ALREADY_INSTALLED" value="4" />
      <member name="downloading_package" c:identifier="FRIDA_PACKAGE_INSTALL_PHASE_DOWNLOADING_PACKAGE" value="5" />
      <member name="package_installed" c:identifier="FRIDA_PACKAGE_INSTALL_PHASE_PACKAGE_INSTALLED" value="6" />
      <member name="resolving_and_installing_all" c:identifier="FRIDA_PACKAGE_INSTALL_PHASE_RESOLVING_AND_INSTALLING_ALL" value="7" />
      <member name="complete" c:identifier="FRIDA_PACKAGE_INSTALL_PHASE_COMPLETE" value="8" />
    </enumeration>
    <enumeration name="PackageRole" c:type="FridaPackageRole" glib:type-name="FridaPackageRole" glib:get-type="frida_package_role_get_type">
      <member name="runtime" c:identifier="FRIDA_PACKAGE_ROLE_RUNTIME" value="0" />
      <member name="development" c:identifier="FRIDA_PACKAGE_ROLE_DEVELOPMENT" value="1" />
      <member name="optional" c:identifier="FRIDA_PACKAGE_ROLE_OPTIONAL" value="2" />
      <member name="peer" c:identifier="FRIDA_PACKAGE_ROLE_PEER" value="3" />
    </enumeration>
    <enumeration name="OutputFormat" c:type="FridaOutputFormat" glib:type-name="FridaOutputFormat" glib:get-type="frida_output_format_get_type">
      <member name="unescaped" c:identifier="FRIDA_OUTPUT_FORMAT_UNESCAPED" value="0" />
      <member name="hex_bytes" c:identifier="FRIDA_OUTPUT_FORMAT_HEX_BYTES" value="1" />
      <member name="c_string" c:identifier="FRIDA_OUTPUT_FORMAT_C_STRING" value="2" />
    </enumeration>
    <enumeration name="BundleFormat" c:type="FridaBundleFormat" glib:type-name="FridaBundleFormat" glib:get-type="frida_bundle_format_get_type">
      <member name="esm" c:identifier="FRIDA_BUNDLE_FORMAT_ESM" value="0" />
      <member name="iife" c:identifier="FRIDA_BUNDLE_FORMAT_IIFE" value="1" />
    </enumeration>
    <enumeration name="TypeCheckMode" c:type="FridaTypeCheckMode" glib:type-name="FridaTypeCheckMode" glib:get-type="frida_type_check_mode_get_type">
      <member name="full" c:identifier="FRIDA_TYPE_CHECK_MODE_FULL" value="0" />
      <member name="none" c:identifier="FRIDA_TYPE_CHECK_MODE_NONE" value="1" />
    </enumeration>
    <enumeration name="SourceMaps" c:type="FridaSourceMaps" glib:type-name="FridaSourceMaps" glib:get-type="frida_source_maps_get_type">
      <member name="included" c:identifier="FRIDA_SOURCE_MAPS_INCLUDED" value="0" />
      <member name="omitted" c:identifier="FRIDA_SOURCE_MAPS_OMITTED" value="1" />
    </enumeration>
    <enumeration name="JsCompression" c:type="FridaJsCompression" glib:type-name="FridaJsCompression" glib:get-type="frida_js_compression_get_type">
      <member name="none" c:identifier="FRIDA_JS_COMPRESSION_NONE" value="0" />
      <member name="terser" c:identifier="FRIDA_JS_COMPRESSION_TERSER" value="1" />
    </enumeration>
    <enumeration name="Realm" c:type="FridaRealm" glib:type-name="FridaRealm" glib:get-type="frida_realm_get_type">
      <member name="native" c:identifier="FRIDA_REALM_NATIVE" value="0" />
      <member name="emulated" c:identifier="FRIDA_REALM_EMULATED" value="1" />
    </enumeration>
    <enumeration name="SessionDetachReason" c:type="FridaSessionDetachReason" glib:type-name="FridaSessionDetachReason" glib:get-type="frida_session_detach_reason_get_type">
      <member name="application_requested" c:identifier="FRIDA_SESSION_DETACH_REASON_APPLICATION_REQUESTED" value="1" />
      <member name="process_replaced" c:identifier="FRIDA_SESSION_DETACH_REASON_PROCESS_REPLACED" value="0" />
      <member name="process_terminated" c:identifier="FRIDA_SESSION_DETACH_REASON_PROCESS_TERMINATED" value="1" />
      <member name="connection_terminated" c:identifier="FRIDA_SESSION_DETACH_REASON_CONNECTION_TERMINATED" value="2" />
      <member name="device_lost" c:identifier="FRIDA_SESSION_DETACH_REASON_DEVICE_LOST" value="3" />
    </enumeration>
    <enumeration name="Scope" c:type="FridaScope" glib:type-name="FridaScope" glib:get-type="frida_scope_get_type">
      <member name="minimal" c:identifier="FRIDA_SCOPE_MINIMAL" value="0" />
      <member name="metadata" c:identifier="FRIDA_SCOPE_METADATA" value="1" />
      <member name="full" c:identifier="FRIDA_SCOPE_FULL" value="2" />
    </enumeration>
    <enumeration name="Stdio" c:type="FridaStdio" glib:type-name="FridaStdio" glib:get-type="frida_stdio_get_type">
      <member name="inherit" c:identifier="FRIDA_STDIO_INHERIT" value="0" />
      <member name="pipe" c:identifier="FRIDA_STDIO_PIPE" value="1" />
    </enumeration>
    <enumeration name="ChildOrigin" c:type="FridaChildOrigin" glib:type-name="FridaChildOrigin" glib:get-type="frida_child_origin_get_type">
      <member name="fork" c:identifier="FRIDA_CHILD_ORIGIN_FORK" value="0" />
      <member name="exec" c:identifier="FRIDA_CHILD_ORIGIN_EXEC" value="1" />
      <member name="spawn" c:identifier="FRIDA_CHILD_ORIGIN_SPAWN" value="2" />
    </enumeration>
    <enumeration name="SnapshotTransport" c:type="FridaSnapshotTransport" glib:type-name="FridaSnapshotTransport" glib:get-type="frida_snapshot_transport_get_type">
      <member name="inline" c:identifier="FRIDA_SNAPSHOT_TRANSPORT_INLINE" value="0" />
      <member name="shared_memory" c:identifier="FRIDA_SNAPSHOT_TRANSPORT_SHARED_MEMORY" value="1" />
    </enumeration>
    <enumeration name="ScriptRuntime" c:type="FridaScriptRuntime" glib:type-name="FridaScriptRuntime" glib:get-type="frida_script_runtime_get_type">
      <member name="default" c:identifier="FRIDA_SCRIPT_RUNTIME_DEFAULT" value="0" />
      <member name="qjs" c:identifier="FRIDA_SCRIPT_RUNTIME_QJS" value="1" />
      <member name="v8" c:identifier="FRIDA_SCRIPT_RUNTIME_V8" value="2" />
    </enumeration>
    <enumeration name="RelayKind" c:type="FridaRelayKind" glib:type-name="FridaRelayKind" glib:get-type="frida_relay_kind_get_type">
      <member name="turn_udp" c:identifier="FRIDA_RELAY_KIND_TURN_UDP" value="0" />
      <member name="turn_tcp" c:identifier="FRIDA_RELAY_KIND_TURN_TCP" value="1" />
      <member name="turn_tls" c:identifier="FRIDA_RELAY_KIND_TURN_TLS" value="2" />
    </enumeration>
    <enumeration name="Error" c:type="FridaError" glib:type-name="FridaError" glib:get-type="frida_error_get_type" glib:error-domain="frida-error-quark">
      <member name="server_not_running" c:identifier="FRIDA_ERROR_SERVER_NOT_RUNNING" value="0" />
      <member name="executable_not_found" c:identifier="FRIDA_ERROR_EXECUTABLE_NOT_FOUND" value="1" />
      <member name="executable_not_supported" c:identifier="FRIDA_ERROR_EXECUTABLE_NOT_SUPPORTED" value="2" />
      <member name="process_not_found" c:identifier="FRIDA_ERROR_PROCESS_NOT_FOUND" value="3" />
      <member name="process_not_responding" c:identifier="FRIDA_ERROR_PROCESS_NOT_RESPONDING" value="4" />
      <member name="invalid_argument" c:identifier="FRIDA_ERROR_INVALID_ARGUMENT" value="5" />
      <member name="invalid_operation" c:identifier="FRIDA_ERROR_INVALID_OPERATION" value="6" />
      <member name="permission_denied" c:identifier="FRIDA_ERROR_PERMISSION_DENIED" value="7" />
      <member name="address_in_use" c:identifier="FRIDA_ERROR_ADDRESS_IN_USE" value="8" />
      <member name="timed_out" c:identifier="FRIDA_ERROR_TIMED_OUT" value="9" />
      <member name="not_supported" c:identifier="FRIDA_ERROR_NOT_SUPPORTED" value="10" />
      <member name="protocol" c:identifier="FRIDA_ERROR_PROTOCOL" value="11" />
      <member name="transport" c:identifier="FRIDA_ERROR_TRANSPORT" value="12" />
    </enumeration>
  </namespace>
</repository>