-- Using Frida library: C:/Users/<USER>/Desktop/zero/app/src/main/cpp/frida/libfrida-core.a
-- Build type: Release
-- Frida install dir: C:/Users/<USER>/Desktop/zero/app/src/main/cpp/frida
-- Frida core library: C:/Users/<USER>/Desktop/zero/app/src/main/cpp/frida/libfrida-core.a
-- Android ABI: arm64-v8a
-- Android NDK: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125
-- Target Android API: android-28
-- Android STL: c++_static
-- Configuring done
-- Generating done
-- Build files have been written to: C:/Users/<USER>/Desktop/zero/app/.cxx/Debug/62b2g1xa/arm64-v8a
